import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, CheckCircle, XCircle, Coffee, ChevronLeft, ChevronRight, CalendarDays, CalendarRange } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { showError } from '../../utils/toastUtils';
import { getAvailableSlots } from '../../services/scheduleApis';
import { getAppointmentsByProvider } from '../../services/appointmentApis';
import type { AvailableSlot } from '../../types/schedule';
import type { Appointment } from '../../types/appointment';

type ViewMode = 'day' | 'week' | 'month';

interface SlotSelectionPanelProps {
  providerId: string;
  selectedDate: string;
  onSlotSelect: (slot: AvailableSlot) => void;
  selectedSlot?: AvailableSlot | null;
  className?: string;
}

export const SlotSelectionPanel: React.FC<SlotSelectionPanelProps> = ({
  providerId,
  selectedDate,
  onSlotSelect,
  selectedSlot,
  className = ''
}) => {
  const [slots, setSlots] = useState<AvailableSlot[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDateSlots, setSelectedDateSlots] = useState<AvailableSlot[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('day');
  const [currentDate, setCurrentDate] = useState(new Date(selectedDate || new Date()));
  const [weekSlots, setWeekSlots] = useState<AvailableSlot[]>([]);
  const [monthSlots, setMonthSlots] = useState<AvailableSlot[]>([]);

  useEffect(() => {
    if (selectedDate) {
      setCurrentDate(new Date(selectedDate));
    }
  }, [selectedDate]);

  useEffect(() => {
    if (providerId) {
      loadSlotsAndAppointments();
    }
  }, [providerId, currentDate, viewMode]);

  const getDateRange = () => {
    const currentDateStr = currentDate.toISOString().split('T')[0];

    switch (viewMode) {
      case 'day':
        return { fromDate: currentDateStr, toDate: currentDateStr };

      case 'week':
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return {
          fromDate: startOfWeek.toISOString().split('T')[0],
          toDate: endOfWeek.toISOString().split('T')[0]
        };

      case 'month':
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        return {
          fromDate: startOfMonth.toISOString().split('T')[0],
          toDate: endOfMonth.toISOString().split('T')[0]
        };

      default:
        return { fromDate: currentDateStr, toDate: currentDateStr };
    }
  };

  const loadSlotsAndAppointments = async () => {
    setLoading(true);
    try {
      const { fromDate, toDate } = getDateRange();

      console.log('🔄 Loading slots and appointments for:', { providerId, fromDate, toDate, viewMode });

      const [slotsData, appointmentsData] = await Promise.all([
        getAvailableSlots(providerId, fromDate, toDate),
        getAppointmentsByProvider(providerId, fromDate, toDate)
      ]);

      console.log('📊 Loaded data:', {
        slotsCount: slotsData.length,
        appointmentsCount: appointmentsData.length,
        viewMode
      });

      setSlots(slotsData);
      setAppointments(appointmentsData);

      // Filter slots based on view mode
      if (viewMode === 'day') {
        const currentDateStr = currentDate.toISOString().split('T')[0];
        const dateSlots = slotsData.filter(slot => slot.date === currentDateStr);
        setSelectedDateSlots(dateSlots);
      } else if (viewMode === 'week') {
        setWeekSlots(slotsData);
      } else if (viewMode === 'month') {
        setMonthSlots(slotsData);
      }

    } catch (error) {
      console.error('Failed to load slots and appointments:', error);
      showError('Failed to load available slots');
    } finally {
      setLoading(false);
    }
  };

  const isSlotBooked = (slot: AvailableSlot): boolean => {
    return appointments.some(apt => 
      apt.appointmentDate.split('T')[0] === slot.date &&
      apt.startTime.includes(slot.startTime.substring(0, 5)) &&
      apt.status !== 'Cancelled'
    );
  };

  const getSlotStatus = (slot: AvailableSlot): 'available' | 'booked' | 'blocked' | 'break' => {
    if (slot.isBlocked) return 'blocked';
    if (slot.blockReason?.toLowerCase().includes('break') || 
        slot.blockReason?.toLowerCase().includes('lunch')) return 'break';
    if (slot.isBooked || isSlotBooked(slot)) return 'booked';
    return 'available';
  };

  const getSlotStatusColor = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300 text-green-800 hover:bg-green-200 cursor-pointer';
      case 'booked':
        return 'bg-red-100 border-red-300 text-red-800 cursor-not-allowed';
      case 'blocked':
        return 'bg-gray-100 border-gray-300 text-gray-600 cursor-not-allowed';
      case 'break':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800 cursor-not-allowed';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-600';
    }
  };

  const getSlotStatusIcon = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'booked':
        return <XCircle size={16} className="text-red-600" />;
      case 'blocked':
        return <XCircle size={16} className="text-gray-600" />;
      case 'break':
        return <Coffee size={16} className="text-yellow-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getSlotStatusText = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'Available';
      case 'booked':
        return 'Booked';
      case 'blocked':
        return slot.blockReason || 'Blocked';
      case 'break':
        return 'Break Time';
      default:
        return 'Unknown';
    }
  };

  const handleSlotClick = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    if (status === 'available') {
      onSlotSelect(slot);
    }
  };

  const formatTime = (timeString: string): string => {
    // Handle both HH:MM:SS and HH:MM formats
    return timeString.substring(0, 5);
  };

  const formatDateHeader = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getViewTitle = (): string => {
    const currentDateStr = currentDate.toISOString().split('T')[0];

    switch (viewMode) {
      case 'day':
        return formatDateHeader(currentDateStr);

      case 'week':
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `Week of ${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;

      case 'month':
        return currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

      default:
        return formatDateHeader(currentDateStr);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);

    switch (viewMode) {
      case 'day':
        newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
    }

    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const getWeekDays = (): Date[] => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const getMonthDays = (): Date[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const days = [];
    for (let i = 1; i <= lastDay.getDate(); i++) {
      days.push(new Date(year, month, i));
    }
    return days;
  };

  const getSlotsForDate = (date: Date): AvailableSlot[] => {
    const dateStr = date.toISOString().split('T')[0];
    return slots.filter(slot => slot.date === dateStr);
  };

  const getSlotsSummaryForDate = (date: Date) => {
    const dateSlots = getSlotsForDate(date);
    return {
      total: dateSlots.length,
      available: dateSlots.filter(s => getSlotStatus(s) === 'available').length,
      booked: dateSlots.filter(s => getSlotStatus(s) === 'booked').length,
      unavailable: dateSlots.filter(s => ['blocked', 'break'].includes(getSlotStatus(s))).length
    };
  };

  if (!providerId) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Provider</h3>
        <p className="text-gray-500">Choose a provider to view available appointment slots.</p>
      </div>
    );
  }

  if (!selectedDate) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Date</h3>
        <p className="text-gray-500">Choose a date to view available appointment slots.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-800">Available Slots</h3>
          </div>

          {/* View Mode Selector */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <Button
              onClick={() => setViewMode('day')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'day'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <Calendar className="h-4 w-4 mr-1" />
              Day
            </Button>
            <Button
              onClick={() => setViewMode('week')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'week'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <CalendarDays className="h-4 w-4 mr-1" />
              Week
            </Button>
            <Button
              onClick={() => setViewMode('month')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'month'
                  ? 'bg-white text-indigo-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <CalendarRange className="h-4 w-4 mr-1" />
              Month
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => navigateDate('prev')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              onClick={() => navigateDate('next')}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <h4 className="text-base font-medium text-gray-800 ml-2">
              {getViewTitle()}
            </h4>
          </div>

          <Button
            onClick={goToToday}
            className="px-3 py-1 text-sm text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-md"
          >
            Today
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading available slots...</p>
          </div>
        ) : (
          <>
            {/* Day View */}
            {viewMode === 'day' && (
              <>
                {selectedDateSlots.length === 0 ? (
                  <div className="text-center py-8">
                    <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No Slots Available</h3>
                    <p className="text-gray-500">
                      No appointment slots are available for the selected date.
                    </p>
                  </div>
                ) : (
          <>
            {/* Legend */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-2">
                  <CheckCircle size={14} className="text-green-600" />
                  <span className="text-gray-600">Available</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-red-600" />
                  <span className="text-gray-600">Booked</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Coffee size={14} className="text-yellow-600" />
                  <span className="text-gray-600">Break Time</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-gray-600" />
                  <span className="text-gray-600">Blocked</span>
                </div>
              </div>
            </div>

            {/* Slots Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-96 overflow-y-auto">
              {selectedDateSlots.map((slot, index) => {
                const isSelected = selectedSlot?.slotId === slot.slotId ||
                  (selectedSlot?.startTime === slot.startTime && selectedSlot?.date === slot.date);
                
                return (
                  <div
                    key={slot.slotId || index}
                    onClick={() => handleSlotClick(slot)}
                    className={`
                      p-3 rounded-lg border-2 transition-all duration-200
                      ${getSlotStatusColor(slot)}
                      ${isSelected ? 'ring-2 ring-indigo-500 ring-offset-2' : ''}
                    `}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="text-sm font-medium">
                        {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                      </div>
                      {getSlotStatusIcon(slot)}
                    </div>
                    <div className="text-xs">
                      {getSlotStatusText(slot)}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {slot.duration} min
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Selected Slot Info */}
            {selectedSlot && (
              <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                <h4 className="text-sm font-medium text-indigo-800 mb-1">Selected Slot:</h4>
                <div className="text-sm text-indigo-700">
                  {formatTime(selectedSlot.startTime)} - {formatTime(selectedSlot.endTime)} 
                  ({selectedSlot.duration} minutes)
                </div>
              </div>
            )}

                    {/* Summary */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-lg font-semibold text-green-600">
                            {selectedDateSlots.filter(s => getSlotStatus(s) === 'available').length}
                          </div>
                          <div className="text-xs text-gray-600">Available</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-red-600">
                            {selectedDateSlots.filter(s => getSlotStatus(s) === 'booked').length}
                          </div>
                          <div className="text-xs text-gray-600">Booked</div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-gray-600">
                            {selectedDateSlots.filter(s => ['blocked', 'break'].includes(getSlotStatus(s))).length}
                          </div>
                          <div className="text-xs text-gray-600">Unavailable</div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}

            {/* Week View */}
            {viewMode === 'week' && (
              <div className="space-y-4">
                {getWeekDays().map((day, index) => {
                  const daySlots = getSlotsForDate(day);
                  const summary = getSlotsSummaryForDate(day);
                  const isToday = day.toDateString() === new Date().toDateString();
                  const dayStr = day.toISOString().split('T')[0];

                  return (
                    <div key={index} className={`border rounded-lg p-4 ${isToday ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'}`}>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <h4 className={`font-medium ${isToday ? 'text-indigo-800' : 'text-gray-800'}`}>
                            {day.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' })}
                          </h4>
                          {isToday && (
                            <span className="px-2 py-1 text-xs bg-indigo-600 text-white rounded-full">Today</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="text-green-600 font-medium">{summary.available} Available</span>
                          <span className="text-red-600">{summary.booked} Booked</span>
                          <span className="text-gray-600">{summary.unavailable} Unavailable</span>
                        </div>
                      </div>

                      {daySlots.length === 0 ? (
                        <p className="text-gray-500 text-sm">No slots available</p>
                      ) : (
                        <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                          {daySlots.slice(0, 12).map((slot, slotIndex) => {
                            const isSelected = selectedSlot?.slotId === slot.slotId ||
                              (selectedSlot?.startTime === slot.startTime && selectedSlot?.date === slot.date);

                            return (
                              <div
                                key={slotIndex}
                                onClick={() => handleSlotClick(slot)}
                                className={`
                                  p-2 rounded text-xs text-center transition-all duration-200
                                  ${getSlotStatusColor(slot)}
                                  ${isSelected ? 'ring-2 ring-indigo-500 ring-offset-1' : ''}
                                `}
                              >
                                <div className="font-medium">
                                  {formatTime(slot.startTime)}
                                </div>
                                <div className="flex items-center justify-center mt-1">
                                  {getSlotStatusIcon(slot)}
                                </div>
                              </div>
                            );
                          })}
                          {daySlots.length > 12 && (
                            <div className="p-2 text-xs text-gray-500 text-center">
                              +{daySlots.length - 12} more
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Month View */}
            {viewMode === 'month' && (
              <div className="space-y-4">
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="text-center text-sm font-medium text-gray-600 py-2">
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {getMonthDays().map((day, index) => {
                    const summary = getSlotsSummaryForDate(day);
                    const isToday = day.toDateString() === new Date().toDateString();
                    const hasSlots = summary.total > 0;

                    return (
                      <div
                        key={index}
                        onClick={() => {
                          setCurrentDate(day);
                          setViewMode('day');
                        }}
                        className={`
                          p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:border-indigo-300
                          ${isToday ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200'}
                          ${hasSlots ? 'hover:bg-gray-50' : 'opacity-60'}
                        `}
                      >
                        <div className={`text-sm font-medium mb-1 ${isToday ? 'text-indigo-800' : 'text-gray-800'}`}>
                          {day.getDate()}
                        </div>

                        {hasSlots ? (
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-green-600">{summary.available}</span>
                              <span className="text-red-600">{summary.booked}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1">
                              <div
                                className="bg-green-500 h-1 rounded-full"
                                style={{
                                  width: `${summary.total > 0 ? (summary.available / summary.total) * 100 : 0}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-400">No slots</div>
                        )}
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span className="text-gray-600">Available slots</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded"></div>
                      <span className="text-gray-600">Booked slots</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">Click on any date to view detailed slots</p>
                </div>
              </div>
            )}

            {/* View Summary */}
            <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4">
                  <span className="text-gray-600">
                    <strong>View:</strong> {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
                  </span>
                  <span className="text-gray-600">
                    <strong>Period:</strong> {getViewTitle()}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-xs">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Available</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Booked</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                    <span>Unavailable</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Selected Slot Info - Show for all views */}
            {selectedSlot && (
              <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                <h4 className="text-sm font-medium text-indigo-800 mb-1">✅ Selected Slot:</h4>
                <div className="text-sm text-indigo-700">
                  <strong>{formatTime(selectedSlot.startTime)} - {formatTime(selectedSlot.endTime)}</strong>
                  ({selectedSlot.duration} minutes) on <strong>{formatDateHeader(selectedSlot.date)}</strong>
                </div>
                <div className="text-xs text-indigo-600 mt-1">
                  Click "Create Appointment" to book this slot
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
