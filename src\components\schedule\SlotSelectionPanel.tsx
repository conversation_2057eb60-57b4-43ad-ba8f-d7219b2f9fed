import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, CheckCircle, XCircle, Coffee } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { showError } from '../../utils/toastUtils';
import { getAvailableSlots } from '../../services/scheduleApis';
import { getAppointmentsByProvider } from '../../services/appointmentApis';
import type { AvailableSlot } from '../../types/schedule';
import type { Appointment } from '../../types/appointment';

interface SlotSelectionPanelProps {
  providerId: string;
  selectedDate: string;
  onSlotSelect: (slot: AvailableSlot) => void;
  selectedSlot?: AvailableSlot | null;
  className?: string;
}

export const SlotSelectionPanel: React.FC<SlotSelectionPanelProps> = ({
  providerId,
  selectedDate,
  onSlotSelect,
  selectedSlot,
  className = ''
}) => {
  const [slots, setSlots] = useState<AvailableSlot[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDateSlots, setSelectedDateSlots] = useState<AvailableSlot[]>([]);

  useEffect(() => {
    if (providerId && selectedDate) {
      loadSlotsAndAppointments();
    }
  }, [providerId, selectedDate]);

  const loadSlotsAndAppointments = async () => {
    setLoading(true);
    try {
      // Load available slots for the date range (current date + 7 days for context)
      const fromDate = selectedDate;
      const toDate = new Date(new Date(selectedDate).getTime() + 7 * 24 * 60 * 60 * 1000)
        .toISOString().split('T')[0];

      const [slotsData, appointmentsData] = await Promise.all([
        getAvailableSlots(providerId, fromDate, toDate),
        getAppointmentsByProvider(providerId, fromDate, toDate)
      ]);

      setSlots(slotsData);
      setAppointments(appointmentsData);

      // Filter slots for the selected date
      const dateSlots = slotsData.filter(slot => slot.date === selectedDate);
      setSelectedDateSlots(dateSlots);

    } catch (error) {
      console.error('Failed to load slots and appointments:', error);
      showError('Failed to load available slots');
    } finally {
      setLoading(false);
    }
  };

  const isSlotBooked = (slot: AvailableSlot): boolean => {
    return appointments.some(apt => 
      apt.appointmentDate.split('T')[0] === slot.date &&
      apt.startTime.includes(slot.startTime.substring(0, 5)) &&
      apt.status !== 'Cancelled'
    );
  };

  const getSlotStatus = (slot: AvailableSlot): 'available' | 'booked' | 'blocked' | 'break' => {
    if (slot.isBlocked) return 'blocked';
    if (slot.blockReason?.toLowerCase().includes('break') || 
        slot.blockReason?.toLowerCase().includes('lunch')) return 'break';
    if (slot.isBooked || isSlotBooked(slot)) return 'booked';
    return 'available';
  };

  const getSlotStatusColor = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300 text-green-800 hover:bg-green-200 cursor-pointer';
      case 'booked':
        return 'bg-red-100 border-red-300 text-red-800 cursor-not-allowed';
      case 'blocked':
        return 'bg-gray-100 border-gray-300 text-gray-600 cursor-not-allowed';
      case 'break':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800 cursor-not-allowed';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-600';
    }
  };

  const getSlotStatusIcon = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'booked':
        return <XCircle size={16} className="text-red-600" />;
      case 'blocked':
        return <XCircle size={16} className="text-gray-600" />;
      case 'break':
        return <Coffee size={16} className="text-yellow-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getSlotStatusText = (slot: AvailableSlot): string => {
    const status = getSlotStatus(slot);
    switch (status) {
      case 'available':
        return 'Available';
      case 'booked':
        return 'Booked';
      case 'blocked':
        return slot.blockReason || 'Blocked';
      case 'break':
        return 'Break Time';
      default:
        return 'Unknown';
    }
  };

  const handleSlotClick = (slot: AvailableSlot) => {
    const status = getSlotStatus(slot);
    if (status === 'available') {
      onSlotSelect(slot);
    }
  };

  const formatTime = (timeString: string): string => {
    // Handle both HH:MM:SS and HH:MM formats
    return timeString.substring(0, 5);
  };

  const formatDateHeader = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!providerId) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Provider</h3>
        <p className="text-gray-500">Choose a provider to view available appointment slots.</p>
      </div>
    );
  }

  if (!selectedDate) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Date</h3>
        <p className="text-gray-500">Choose a date to view available appointment slots.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-800">Available Slots</h3>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          {formatDateHeader(selectedDate)}
        </p>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading available slots...</p>
          </div>
        ) : selectedDateSlots.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">No Slots Available</h3>
            <p className="text-gray-500">
              No appointment slots are available for the selected date.
            </p>
          </div>
        ) : (
          <>
            {/* Legend */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-2">
                  <CheckCircle size={14} className="text-green-600" />
                  <span className="text-gray-600">Available</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-red-600" />
                  <span className="text-gray-600">Booked</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Coffee size={14} className="text-yellow-600" />
                  <span className="text-gray-600">Break Time</span>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle size={14} className="text-gray-600" />
                  <span className="text-gray-600">Blocked</span>
                </div>
              </div>
            </div>

            {/* Slots Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-96 overflow-y-auto">
              {selectedDateSlots.map((slot, index) => {
                const isSelected = selectedSlot?.slotId === slot.slotId ||
                  (selectedSlot?.startTime === slot.startTime && selectedSlot?.date === slot.date);
                
                return (
                  <div
                    key={slot.slotId || index}
                    onClick={() => handleSlotClick(slot)}
                    className={`
                      p-3 rounded-lg border-2 transition-all duration-200
                      ${getSlotStatusColor(slot)}
                      ${isSelected ? 'ring-2 ring-indigo-500 ring-offset-2' : ''}
                    `}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="text-sm font-medium">
                        {formatTime(slot.startTime)} - {formatTime(slot.endTime)}
                      </div>
                      {getSlotStatusIcon(slot)}
                    </div>
                    <div className="text-xs">
                      {getSlotStatusText(slot)}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {slot.duration} min
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Selected Slot Info */}
            {selectedSlot && (
              <div className="mt-4 p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                <h4 className="text-sm font-medium text-indigo-800 mb-1">Selected Slot:</h4>
                <div className="text-sm text-indigo-700">
                  {formatTime(selectedSlot.startTime)} - {formatTime(selectedSlot.endTime)} 
                  ({selectedSlot.duration} minutes)
                </div>
              </div>
            )}

            {/* Summary */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-semibold text-green-600">
                    {selectedDateSlots.filter(s => getSlotStatus(s) === 'available').length}
                  </div>
                  <div className="text-xs text-gray-600">Available</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-red-600">
                    {selectedDateSlots.filter(s => getSlotStatus(s) === 'booked').length}
                  </div>
                  <div className="text-xs text-gray-600">Booked</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-gray-600">
                    {selectedDateSlots.filter(s => ['blocked', 'break'].includes(getSlotStatus(s))).length}
                  </div>
                  <div className="text-xs text-gray-600">Unavailable</div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
