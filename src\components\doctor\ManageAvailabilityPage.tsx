// pages/doctor/ManageAvailabilityPage.tsx
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { getDoctorById } from "../../services/doctorApis";
import { getDepartmentById, updateDepartment } from "../../services/departmentApis";

const ManageAvailabilityPage = () => {
  const { id } = useParams();
  const [availability, setAvailability] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      const docRes = await getDoctorById(id!);
      if (docRes.success) {
        const deptRes = await getDepartmentById(docRes.data.specialization);
        if (deptRes.success) {
          setAvailability(deptRes.data.operatingHours || []);
        }
      }
    };
    fetchData();
  }, [id]);

  const toggleOperating = (index: number) => {
    const updated = [...availability];
    updated[index].isOperating = !updated[index].isOperating;
    setAvailability(updated);
  };

  const saveAvailability = async () => {
    const docRes = await getDoctorById(id!);
    if (docRes.success) {
      await updateDepartment(docRes.data.specialization, {
        operatingHours: availability,
      });
      alert("Availability updated!");
    }
  };

  return (
    <div className="p-6 max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Manage Availability</h2>
      <ul className="space-y-3">
        {availability.map((entry: any, idx) => (
          <li key={idx} className="flex items-center justify-between border p-3 rounded">
            <span>{entry.dayOfWeek}</span>
            <button
              onClick={() => toggleOperating(idx)}
              className={`px-3 py-1 rounded ${
                entry.isOperating ? "bg-green-500" : "bg-gray-400"
              } text-white`}
            >
              {entry.isOperating ? "Available" : "Unavailable"}
            </button>
          </li>
        ))}
      </ul>
      <button
        onClick={saveAvailability}
        className="mt-4 bg-blue-600 text-white px-4 py-2 rounded"
      >
        Save Changes
      </button>
    </div>
  );
};

export default ManageAvailabilityPage;
