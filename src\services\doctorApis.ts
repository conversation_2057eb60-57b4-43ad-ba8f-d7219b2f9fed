import axios from 'axios';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
  if (token) config.headers.Authorization = `Bearer ${token}`;

  const facilityId = localStorage.getItem('selectedFacilityId');
  if (facilityId) config.headers['X-Facility-Id'] = facilityId;

  return config;
});

// ========================
// Interfaces
// ========================

export interface DoctorDTO {
  specialization: string;          // UUID string
  fullName: string;
  registrationNumber: string;
  registrationState?: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string[];       // ✅ should be string[] NOT string
  isActive: boolean;
  createdAt?: string;
}


export interface ApiResponseDoctorDTO {
  success: boolean;
  data: DoctorDTO;
  message?: string;
  error?: string;
}

export interface ApiResponseDoctorList {
  success: boolean;
  data: DoctorDTO[];
  message?: string;
  error?: string;
}

// ========================
// API Calls
// ========================

// GET /doctors
export const getAllDoctors = async (): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/doctors`);
    return {
      success: true,
      data: res.data
    };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message
    };
  }
};

// GET /doctors/active
export const getActiveDoctors = async (): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/doctors/active`);
    return {
      success: true,
      data: res.data
    };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message
    };
  }
};

// GET /doctors/:id
export const getDoctorById = async (id: string): Promise<ApiResponseDoctorDTO> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/doctors/${id}`);
    return {
      success: true,
      data: res.data
    };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO,
      error: err.response?.data?.message || err.message
    };
  }
};

// GET /doctors/specialization/:specId
export const getDoctorsBySpecialization = async (specId: string): Promise<ApiResponseDoctorList> => {
  try {
    const res = await axios.get(`${BASE_URL}/api/doctors/specialization/${specId}`);
    return {
      success: true,
      data: res.data
    };
  } catch (err: any) {
    return {
      success: false,
      data: [],
      error: err.response?.data?.message || err.message
    };
  }
};

// POST /doctors
export const createDoctor = async (payload: DoctorDTO): Promise<ApiResponseDoctorDTO> => {
  try {
    const res = await axios.post(`${BASE_URL}/api/doctors`, payload);
    return {
      success: true,  
      data: res.data,
      message: 'Doctor created successfully'
    };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO,
      error: err.response?.data?.message || err.message
    };
  }
};

// PUT /doctors/:id
export const updateDoctor = async (id: string, payload: DoctorDTO): Promise<ApiResponseDoctorDTO> => {
  try {
    const res = await axios.put(`${BASE_URL}/api/doctors/${id}`, payload);
    return {
      success: true,
      data: res.data,
      message: 'Doctor updated successfully'
    };
  } catch (err: any) {
    return {
      success: false,
      data: {} as DoctorDTO,
      error: err.response?.data?.message || err.message
    };
  }
};

// DELETE /doctors/:id
export const deleteDoctor = async (
  id: string
): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    await axios.delete(`${BASE_URL}/api/doctors/${id}`);
    return {
      success: true,
      message: 'Doctor deleted successfully'
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.response?.data?.message || err.message
    };
  }
};
