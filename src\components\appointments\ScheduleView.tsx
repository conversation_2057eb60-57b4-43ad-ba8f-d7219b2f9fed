import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Calendar, Clock } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { getAppointments } from '../../services/appointmentApis';
import type { Appointment } from '../../types/appointment';
import { showError } from '../../utils/toastUtils';

interface ScheduleViewProps {
  onAppointmentSelect?: (appointment: Appointment) => void;
}

interface DaySchedule {
  date: Date;
  dayName: string;
  dayNumber: number;
  appointments: Appointment[];
  appointmentCount: number;
}

export const ScheduleView: React.FC<ScheduleViewProps> = ({
  onAppointmentSelect
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    loadAppointments();
  }, [currentDate]);

  const loadAppointments = async () => {
    setLoading(true);
    try {
      // Get appointments for the current week
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      const response = await getAppointments({
        dateFrom: startOfWeek.toISOString().split('T')[0],
        dateTo: endOfWeek.toISOString().split('T')[0],
        size: 1000
      });
      
      setAppointments(response.results || []);
    } catch (error) {
      console.error('Failed to load appointments:', error);
      showError('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  const getWeekDays = (): DaySchedule[] => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    
    const days: DaySchedule[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      
      const dayAppointments = appointments.filter(apt => {
        const aptDate = new Date(apt.appointmentDate);
        return aptDate.toDateString() === date.toDateString();
      });

      days.push({
        date,
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }).toUpperCase(),
        dayNumber: date.getDate(),
        appointments: dayAppointments,
        appointmentCount: dayAppointments.length
      });
    }
    return days;
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentDate(newDate);
  };

  const formatDateRange = (): string => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const year = startOfWeek.getFullYear();
    
    if (startMonth === endMonth) {
      return `${startOfWeek.getDate()} - ${endOfWeek.getDate()} ${startMonth}, ${year}`;
    } else {
      return `${startOfWeek.getDate()} ${startMonth} - ${endOfWeek.getDate()} ${endMonth}, ${year}`;
    }
  };

  const getAppointmentColor = (appointment: Appointment): string => {
    switch (appointment.status) {
      case 'Scheduled':
        return 'bg-blue-500';
      case 'Confirmed':
        return 'bg-green-500';
      case 'In Progress':
        return 'bg-yellow-500';
      case 'Completed':
        return 'bg-gray-500';
      case 'Cancelled':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const formatTime = (timeString: string): string => {
    if (timeString.includes('T')) {
      return new Date(timeString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    }
    return timeString;
  };

  const weekDays = getWeekDays();
  const selectedDaySchedule = weekDays.find(day => 
    day.date.toDateString() === selectedDate.toDateString()
  ) || weekDays[0];

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => navigateWeek('prev')}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronLeft size={16} />
              </Button>
              <h2 className="text-lg font-semibold text-gray-900">{formatDateRange()}</h2>
              <Button
                onClick={() => navigateWeek('next')}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronRight size={16} />
              </Button>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Today
            </Button>
            <Button className="px-3 py-1 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700">
              Week
            </Button>
            <Button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
              Month
            </Button>
            <Button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
              Year
            </Button>
            <Button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
              Filter
            </Button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Week Overview */}
        <div className="w-80 border-r border-gray-200 p-6">
          <div className="grid grid-cols-7 gap-2 mb-6">
            {weekDays.map((day) => (
              <div
                key={day.date.toISOString()}
                className={`text-center p-3 rounded-lg cursor-pointer transition-colors ${
                  day.date.toDateString() === selectedDate.toDateString()
                    ? 'bg-purple-100 text-purple-700'
                    : day.date.toDateString() === new Date().toDateString()
                    ? 'bg-blue-50 text-blue-700'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedDate(day.date)}
              >
                <div className="text-xs font-medium text-gray-600 mb-1">{day.dayName}</div>
                <div className="text-lg font-semibold">{day.dayNumber}</div>
                <div className="text-xs text-gray-500">{day.appointmentCount} appointments</div>
              </div>
            ))}
          </div>

          {/* Stats */}
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">Total</span>
                <span className="text-lg font-semibold text-gray-900">13</span>
              </div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-green-600">Completed</span>
                <span className="text-lg font-semibold text-green-700">4</span>
              </div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-yellow-600">Process</span>
                <span className="text-lg font-semibold text-yellow-700">4</span>
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-600">Waiting</span>
                <span className="text-lg font-semibold text-blue-700">5</span>
              </div>
            </div>
          </div>
        </div>

        {/* Day Schedule */}
        <div className="flex-1 p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {selectedDate.toLocaleDateString('en-US', { 
                weekday: 'long', 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
              })}
            </h3>
            <p className="text-sm text-gray-600">
              {selectedDaySchedule.appointmentCount} appointments scheduled
            </p>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : (
            <div className="space-y-3">
              {selectedDaySchedule.appointments.map((appointment) => (
                <div
                  key={appointment.appointmentId}
                  className="flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onAppointmentSelect?.(appointment)}
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <div className={`w-3 h-3 rounded-full ${getAppointmentColor(appointment)}`}></div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">
                          {appointment.patient?.firstName} {appointment.patient?.lastName}
                        </span>
                        <span className="text-sm text-gray-500">
                          {appointment.type}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {appointment.provider?.title} {appointment.provider?.firstName} {appointment.provider?.lastName}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTime(appointment.startTime)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {appointment.status}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {selectedDaySchedule.appointments.length === 0 && (
                <div className="text-center py-12">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">No appointments scheduled for this day</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
