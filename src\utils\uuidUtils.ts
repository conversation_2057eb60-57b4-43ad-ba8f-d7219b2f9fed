/**
 * UUID Utility Functions for Schedule Management
 * Provides helper functions to generate and validate UUIDs in the required format
 */

/**
 * Generates a random UUID v4 in the format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
 * @returns {string} A properly formatted UUID
 */
export const generateUUID = (): string => {
  // Generate random hex characters
  const hex = () => Math.floor(Math.random() * 16).toString(16);
  
  // Generate UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  // where x is any hexadecimal digit and y is one of 8, 9, A, or B
  const uuid = [
    // First group: 8 characters
    Array.from({ length: 8 }, hex).join(''),
    // Second group: 4 characters
    Array.from({ length: 4 }, hex).join(''),
    // Third group: 4 characters (version 4)
    '4' + Array.from({ length: 3 }, hex).join(''),
    // Fourth group: 4 characters (variant bits)
    ['8', '9', 'a', 'b'][Math.floor(Math.random() * 4)] + Array.from({ length: 3 }, hex).join(''),
    // Fifth group: 12 characters
    Array.from({ length: 12 }, hex).join('')
  ].join('-');

  return uuid.toUpperCase();
};

/**
 * Generates a consultant UUID in the specific format used by the system
 * @returns {string} A consultant UUID in the format FC415F36-3366-6bf5-A07b-b9E0D4Bddbdd
 */
export const generateConsultantUUID = (): string => {
  return generateUUID();
};

/**
 * Validates if a string is a properly formatted UUID
 * @param {string} uuid - The UUID string to validate
 * @returns {boolean} True if the UUID is valid, false otherwise
 */
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validates if a consultant ID follows the required UUID format
 * @param {string} consultantId - The consultant ID to validate
 * @returns {boolean} True if the consultant ID is a valid UUID, false otherwise
 */
export const isValidConsultantId = (consultantId: string): boolean => {
  return isValidUUID(consultantId);
};

/**
 * Formats a UUID to ensure proper case (uppercase)
 * @param {string} uuid - The UUID to format
 * @returns {string} The formatted UUID in uppercase
 */
export const formatUUID = (uuid: string): string => {
  if (!isValidUUID(uuid)) {
    throw new Error(`Invalid UUID format: ${uuid}`);
  }
  return uuid.toUpperCase();
};

/**
 * Generates multiple consultant UUIDs for testing purposes
 * @param {number} count - Number of UUIDs to generate
 * @returns {string[]} Array of generated consultant UUIDs
 */
export const generateMultipleConsultantUUIDs = (count: number): string[] => {
  return Array.from({ length: count }, () => generateConsultantUUID());
};

/**
 * Creates a consultant ID from a name (for testing/demo purposes)
 * This generates a deterministic UUID based on the name
 * @param {string} name - The consultant name
 * @returns {string} A UUID generated from the name
 */
export const createConsultantIdFromName = (name: string): string => {
  // Simple hash function to create deterministic UUID from name
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Convert hash to hex and pad
  const hexHash = Math.abs(hash).toString(16).padStart(8, '0');
  
  // Create UUID-like format using the hash
  const uuid = [
    hexHash.substring(0, 8),
    hexHash.substring(0, 4),
    '4' + hexHash.substring(1, 4), // Version 4
    '8' + hexHash.substring(1, 4), // Variant bits
    hexHash.repeat(3).substring(0, 12)
  ].join('-');
  
  return uuid.toUpperCase();
};

/**
 * Sample consultant UUIDs for testing and development
 */
export const SAMPLE_CONSULTANT_UUIDS = {
  CARDIOLOGY_CONSULTANT: 'FC415F36-3366-6bf5-A07b-b9E0D4Bddbdd',
  ORTHOPEDICS_CONSULTANT: 'A8B2C4D6-1234-5678-9ABC-DEF012345678',
  PEDIATRICS_CONSULTANT: 'B7C8D9E0-5678-9ABC-DEF0-123456789ABC',
  NEUROLOGY_CONSULTANT: 'C9D0E1F2-9ABC-DEF0-1234-56789ABCDEF0',
  EMERGENCY_CONSULTANT: 'D1E2F3A4-DEF0-1234-5678-9ABCDEF01234',
  GYNECOLOGY_CONSULTANT: 'E3F4A5B6-1234-5678-9ABC-DEF012345678'
} as const;

/**
 * Gets a random sample consultant UUID
 * @returns {string} A random consultant UUID from the sample list
 */
export const getRandomSampleConsultantUUID = (): string => {
  const uuids = Object.values(SAMPLE_CONSULTANT_UUIDS);
  return uuids[Math.floor(Math.random() * uuids.length)];
};

/**
 * Validates and formats a consultant ID for API payload
 * @param {string} consultantId - The consultant ID to validate and format
 * @returns {string} The validated and formatted consultant ID
 * @throws {Error} If the consultant ID is invalid
 */
export const validateAndFormatConsultantId = (consultantId: string): string => {
  if (!consultantId) {
    throw new Error('Consultant ID is required');
  }
  
  if (!isValidConsultantId(consultantId)) {
    throw new Error(`Invalid consultant ID format: ${consultantId}. Expected UUID format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX`);
  }
  
  return formatUUID(consultantId);
};

/**
 * Creates a schedule payload with validated consultant UUID
 * @param {object} scheduleData - The schedule data
 * @param {string} scheduleData.consultantId - The consultant ID
 * @param {string[]} scheduleData.daysOfWeek - Days of the week
 * @param {string} scheduleData.startTime - Start time with nanoseconds
 * @param {string} scheduleData.endTime - End time with nanoseconds
 * @param {number} scheduleData.slotDuration - Slot duration in minutes
 * @param {string} scheduleData.effectiveFrom - Effective from date
 * @param {string} scheduleData.effectiveTo - Effective to date
 * @returns {object} Validated schedule payload
 */
export const createSchedulePayload = (scheduleData: {
  consultantId: string;
  daysOfWeek: string[];
  startTime: string;
  endTime: string;
  slotDuration: number;
  effectiveFrom: string;
  effectiveTo: string;
}) => {
  return {
    ...scheduleData,
    consultantId: validateAndFormatConsultantId(scheduleData.consultantId)
  };
};

/**
 * Utility to convert time to nanosecond format required by API
 * @param {string} time - Time in HH:MM:SS format
 * @returns {string} Time in HH:MM:SS.nnnnnnnnn format
 */
export const formatTimeWithNanoseconds = (time: string): string => {
  // Check if time already has nanoseconds
  if (time.includes('.')) {
    return time;
  }
  
  // Add nanoseconds if not present
  return `${time}.000000000`;
};

/**
 * Creates a complete schedule configuration payload with proper formatting
 * @param {object} config - Schedule configuration
 * @returns {object} Properly formatted schedule payload
 */
export const createFormattedSchedulePayload = (config: {
  consultantId: string;
  daysOfWeek: string[];
  startTime: string;
  endTime: string;
  slotDuration: number;
  effectiveFrom: string;
  effectiveTo: string;
}) => {
  return {
    consultantId: validateAndFormatConsultantId(config.consultantId),
    daysOfWeek: config.daysOfWeek,
    startTime: formatTimeWithNanoseconds(config.startTime),
    endTime: formatTimeWithNanoseconds(config.endTime),
    slotDuration: config.slotDuration,
    effectiveFrom: config.effectiveFrom,
    effectiveTo: config.effectiveTo
  };
};
