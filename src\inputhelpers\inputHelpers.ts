export const allowOnlyNumbersWithLimit = (maxLength: number) => (
    e: React.KeyboardEvent<HTMLInputElement>
) => {
    const key = e.key;
    const input = e.currentTarget;

    // Allow navigation and control keys
    if (["Backspace", "Tab", "ArrowLeft", "ArrowRight"].includes(key)) return;

    const isNumber = /^\d$/.test(key);
    const willExceed = input.value.length >= maxLength;

    if (!isNumber || willExceed) {
        e.preventDefault();
        return;
    }

    // Disallow 0 as the first digit
    if (input.value.length === 0 && key === "0") {
        e.preventDefault();
    }
};