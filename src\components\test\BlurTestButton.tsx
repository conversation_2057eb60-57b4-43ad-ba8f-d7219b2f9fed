import React, { useState } from 'react';

export const BlurTestButton: React.FC = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      {/* Test Button - Fixed position */}
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setShowModal(true)}
          className="bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors"
        >
          🔍 Test Blur Effect
        </button>
      </div>

      {/* Test Modal with Blur Backdrop */}
      {showModal && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 blur-backdrop flex items-center justify-center z-50"
          onClick={() => setShowModal(false)}
        >
          <div 
            className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-xl font-bold text-gray-800 mb-4">✅ Blur Effect Test</h2>
            <div className="space-y-3 text-gray-600">
              <p>
                <strong>If you can see this modal clearly with a blurred background, the blur effects are working!</strong>
              </p>
              <p>
                The background should be:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Blurred (8px blur effect)</li>
                <li>Darkened with 50% opacity</li>
                <li>Content behind should be unreadable</li>
              </ul>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-4">
                <p className="text-green-800 text-sm">
                  ✅ <strong>Success!</strong> Blur effects are now working across all modals and side panels.
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
              >
                🎉 Blur Works!
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
