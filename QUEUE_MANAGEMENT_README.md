# 🚶‍♂️ Queue Management & Wait Time Estimation System

## 📋 Overview

The Queue Management and Wait Time Estimation system is a comprehensive solution for managing patient queues across different healthcare services in the MeghaSanjeevini platform. It provides real-time queue monitoring, intelligent wait time predictions, and efficient patient flow management.

## ✨ Features Implemented

### 🎯 Core Queue Management
- ✅ **Real-time Queue Monitoring** - Live updates of patient queues across all services
- ✅ **Multi-Service Support** - Handles 8 different service types (Consultation, Emergency, Laboratory, etc.)
- ✅ **Priority-based Queuing** - Emergency and urgent patients get priority
- ✅ **Queue Status Tracking** - Waiting → Called → In Service → Completed workflow
- ✅ **Patient Information Display** - Complete patient details with contact information

### ⏱️ Wait Time Estimation
- ✅ **Intelligent Wait Time Calculation** - Based on queue position, service times, and priority
- ✅ **Priority-based Estimates** - Different wait times for different priority levels
- ✅ **Confidence Scoring** - Accuracy indicators for wait time predictions
- ✅ **Real-time Updates** - Wait times update every 2 minutes automatically
- ✅ **Historical Data Integration** - Uses past service times for better predictions

### 📊 Analytics & Statistics
- ✅ **Queue Statistics Dashboard** - Comprehensive analytics and metrics
- ✅ **Service Performance Tracking** - Individual service statistics
- ✅ **Hourly Activity Charts** - Visual representation of queue activity
- ✅ **Peak Hours Identification** - Automatic detection of busy periods
- ✅ **Patient Satisfaction Tracking** - Service quality metrics

### 🔄 Real-time Features
- ✅ **Auto-refresh Capability** - Automatic data updates every 30 seconds
- ✅ **Live Status Updates** - Real-time queue status changes
- ✅ **Notification System** - Alerts for important queue events
- ✅ **WebSocket Ready** - Prepared for real-time WebSocket integration

## 🏗️ Architecture

### 📁 File Structure
```
src/
├── components/appointments/
│   ├── QueueManagement.tsx          # Main queue management component
│   ├── QueueCard.tsx                # Individual service queue cards
│   ├── WaitTimeDisplay.tsx          # Wait time estimation display
│   ├── QueueStatsDashboard.tsx      # Analytics dashboard
│   └── QueueActions.tsx             # Queue action buttons
├── types/
│   └── queue.ts                     # Queue-related TypeScript interfaces
├── services/
│   └── queueApis.ts                 # Queue management API calls
├── store/
│   └── queueStore.ts                # Zustand state management
├── mockData/
│   └── queueMockData.ts             # Mock data for development
├── utils/
│   └── queueUtils.ts                # Queue utility functions
└── pages/
    └── QueueManagementPage.tsx      # Main queue management page
```

### 🔧 Technology Stack
- **Frontend Framework**: React 18 with TypeScript
- **State Management**: Zustand
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **API Layer**: Axios with mock data support
- **Routing**: React Router v6

## 🚀 Getting Started

### 1. Navigation
Access the queue management system at: `http://localhost:5173/queue`

### 2. Main Features

#### 📊 Overview Mode
- View all service queues at a glance
- See current queue lengths and wait times
- Identify services with high priority patients
- Monitor peak hour indicators

#### 🔍 Detailed Mode
- Focus on a specific service type
- View complete patient queue with details
- See currently serving patient information
- Access patient contact information

#### 📈 Statistics Mode
- Comprehensive analytics dashboard
- Service performance metrics
- Hourly activity charts
- Peak hours and performance indicators

### 3. Queue Operations

#### For Staff/Administrators:
- **Call Patient**: Move patient from waiting to called status
- **Start Service**: Begin patient service
- **Complete Service**: Mark service as completed
- **Skip Patient**: Temporarily skip a patient
- **Mark No Show**: Handle patients who don't show up

#### For Patients:
- **View Wait Times**: See estimated wait times for all services
- **Check Queue Position**: Know their position in the queue
- **Priority-based Estimates**: Get accurate wait times based on their priority level

## 📊 Data Models

### Queue Entry
```typescript
interface QueueEntry {
  queueId: string;
  appointmentId?: string;
  patientId: string;
  patientName: string;
  queueNumber: number;
  serviceType: ServiceType;
  priority: AppointmentPriority;
  status: QueueStatus;
  estimatedWaitTime: number;
  estimatedServiceTime: string;
  joinedAt: string;
  facilityId: string;
  // ... additional fields
}
```

### Service Types
- **Consultation** - General medical consultations
- **Emergency** - Emergency medical services
- **Laboratory** - Lab tests and sample collection
- **Pharmacy** - Medication dispensing
- **Diagnostic** - Diagnostic procedures
- **Radiology** - Imaging services
- **Registration** - Patient registration
- **Procedure** - Medical procedures

### Queue Statuses
- **Waiting** - Patient in queue, waiting to be called
- **Called** - Patient has been called for service
- **InService** - Patient is currently being served
- **Completed** - Service completed successfully
- **NoShow** - Patient didn't show up when called
- **Skipped** - Patient temporarily skipped

## 🔄 API Integration

### Mock Data vs Real API
The system is currently configured to use mock data (`USE_MOCK_DATA = true`). To switch to real API:

1. Set `USE_MOCK_DATA = false` in `src/services/queueApis.ts`
2. Ensure backend APIs are available at the configured endpoints
3. Update `BASE_URL` if needed

### Key API Endpoints
```typescript
// Get service queue
GET /queue/{serviceType}?facilityId={id}&date={date}

// Add to queue
POST /queue/{serviceType}/add

// Update queue status
PATCH /queue/{serviceType}/{queueId}

// Get wait time estimation
GET /queue/{serviceType}/wait-time?facilityId={id}

// Get queue statistics
GET /queue/stats?facilityId={id}
```

## 🎨 UI Components

### QueueCard
- Displays individual service queue information
- Shows current queue length and wait times
- Indicates emergency patients with badges
- Provides quick access to detailed view

### WaitTimeDisplay
- Shows estimated wait times with confidence scores
- Priority-based wait time breakdown
- Real-time updates with trend indicators
- Visual color coding for wait time severity

### QueueStatsDashboard
- Comprehensive analytics and metrics
- Service performance comparisons
- Hourly activity visualization
- Peak hours and performance indicators

## 🔧 Configuration

### Auto-refresh Settings
```typescript
// Queue data refresh interval (30 seconds)
const QUEUE_REFRESH_INTERVAL = 30000;

// Wait time refresh interval (2 minutes)
const WAIT_TIME_REFRESH_INTERVAL = 120000;
```

### Priority Weights
```typescript
const PRIORITY_MULTIPLIERS = {
  Emergency: 0.1,    // Immediate service
  Urgent: 0.3,       // 30% of normal wait time
  High: 0.6,         // 60% of normal wait time
  Normal: 1.0,       // Standard wait time
  Low: 1.5           // 150% of normal wait time
};
```

## 📱 Responsive Design

The queue management system is fully responsive and works on:
- **Desktop** - Full dashboard with all features
- **Tablet** - Optimized layout with touch-friendly controls
- **Mobile** - Compact view with essential information

## 🔮 Future Enhancements

### Planned Features
- [ ] **WebSocket Integration** - Real-time updates without polling
- [ ] **SMS Notifications** - Automatic patient notifications
- [ ] **QR Code Integration** - Patient self-check-in
- [ ] **Voice Announcements** - Audio patient calling system
- [ ] **Appointment Integration** - Seamless appointment-to-queue flow
- [ ] **Staff Workload Balancing** - Intelligent staff assignment
- [ ] **Predictive Analytics** - ML-based wait time predictions

### Integration Points
- [ ] **External HMS Systems** - Integration with hospital management systems
- [ ] **Payment Gateway** - Queue-based payment processing
- [ ] **Telemedicine** - Virtual queue management
- [ ] **Mobile App** - Dedicated patient mobile application

## 🐛 Troubleshooting

### Common Issues

1. **Queue data not loading**
   - Check if mock data is enabled
   - Verify API endpoints are accessible
   - Check browser console for errors

2. **Wait times not updating**
   - Ensure auto-refresh is enabled
   - Check network connectivity
   - Verify API response format

3. **Priority patients not showing correctly**
   - Check priority assignment logic
   - Verify queue sorting algorithm
   - Review priority color coding

### Debug Mode
Enable debug logging by setting:
```typescript
const DEBUG_MODE = true;
```

## 📞 Support

For technical support or feature requests:
- **Development Team**: MeghaSanjeevini Frontend Team
- **Documentation**: This README and inline code comments
- **Issue Tracking**: GitHub Issues (if applicable)

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Compatibility**: React 18+, TypeScript 4.9+
