import React, { useState } from "react";
import { 
  Settings, 
  X, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  Volume2,
  Save,
  RotateCcw,
  Users,
  Timer
} from "lucide-react";
import { Button } from "../../commonfields/Button";
import { Select } from "../../commonfields/Select";

interface QueueSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SettingsConfig {
  autoRefresh: boolean;
  refreshInterval: number;
  soundNotifications: boolean;
  showEmergencyAlerts: boolean;
  theme: 'light' | 'dark' | 'auto';
  defaultView: 'overview' | 'detailed' | 'statistics';
  priorityWeights: {
    emergency: number;
    urgent: number;
    high: number;
    normal: number;
    low: number;
  };
  serviceTimeDefaults: {
    consultation: number;
    emergency: number;
    laboratory: number;
    pharmacy: number;
    diagnostic: number;
    radiology: number;
    registration: number;
    procedure: number;
  };
}

export const QueueSettings: React.FC<QueueSettingsProps> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState<SettingsConfig>({
    autoRefresh: true,
    refreshInterval: 30,
    soundNotifications: true,
    showEmergencyAlerts: true,
    theme: 'light',
    defaultView: 'overview',
    priorityWeights: {
      emergency: 0.1,
      urgent: 0.3,
      high: 0.6,
      normal: 1.0,
      low: 1.5
    },
    serviceTimeDefaults: {
      consultation: 18,
      emergency: 30,
      laboratory: 10,
      pharmacy: 5,
      diagnostic: 25,
      radiology: 20,
      registration: 8,
      procedure: 45
    }
  });

  const [activeTab, setActiveTab] = useState<'general' | 'display' | 'priorities' | 'services'>('general');

  const handleSave = () => {
    // Save settings to localStorage or send to API
    localStorage.setItem('queueSettings', JSON.stringify(settings));
    
    // Apply settings immediately
    if (settings.autoRefresh) {
      // Trigger auto-refresh with new interval
      console.log(`Auto-refresh enabled with ${settings.refreshInterval}s interval`);
    }
    
    // Show success message
    alert('Settings saved successfully!');
    onClose();
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      // Reset to default settings
      setSettings({
        autoRefresh: true,
        refreshInterval: 30,
        soundNotifications: true,
        showEmergencyAlerts: true,
        theme: 'light',
        defaultView: 'overview',
        priorityWeights: {
          emergency: 0.1,
          urgent: 0.3,
          high: 0.6,
          normal: 1.0,
          low: 1.5
        },
        serviceTimeDefaults: {
          consultation: 18,
          emergency: 30,
          laboratory: 10,
          pharmacy: 5,
          diagnostic: 25,
          radiology: 20,
          registration: 8,
          procedure: 45
        }
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Settings className="text-gray-600" size={24} />
            <h2 className="text-xl font-semibold text-gray-800">Queue Management Settings</h2>
          </div>
          <Button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={20} />
          </Button>
        </div>

        <div className="flex">
          {/* Sidebar Tabs */}
          <div className="w-64 bg-gray-50 border-r border-gray-200">
            <nav className="p-4 space-y-2">
              <button
                onClick={() => setActiveTab('general')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left ${
                  activeTab === 'general' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Clock size={16} />
                <span>General</span>
              </button>
              <button
                onClick={() => setActiveTab('display')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left ${
                  activeTab === 'display' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Eye size={16} />
                <span>Display</span>
              </button>
              <button
                onClick={() => setActiveTab('priorities')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left ${
                  activeTab === 'priorities' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Users size={16} />
                <span>Priorities</span>
              </button>
              <button
                onClick={() => setActiveTab('services')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left ${
                  activeTab === 'services' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Timer size={16} />
                <span>Service Times</span>
              </button>
            </nav>
          </div>

          {/* Content Area */}
          <div className="flex-1 p-6 overflow-y-auto max-h-[70vh]">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-800">General Settings</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Auto Refresh</label>
                      <p className="text-xs text-gray-500">Automatically refresh queue data</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.autoRefresh}
                      onChange={(e) => setSettings({...settings, autoRefresh: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Refresh Interval (seconds)
                    </label>
                    <Select
                      value={settings.refreshInterval}
                      onChange={(e) => setSettings({...settings, refreshInterval: Number(e.target.value)})}
                      disabled={!settings.autoRefresh}
                    >
                      <option value={15}>15 seconds</option>
                      <option value={30}>30 seconds</option>
                      <option value={60}>1 minute</option>
                      <option value={120}>2 minutes</option>
                      <option value={300}>5 minutes</option>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Sound Notifications</label>
                      <p className="text-xs text-gray-500">Play sounds for queue updates</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.soundNotifications}
                      onChange={(e) => setSettings({...settings, soundNotifications: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Emergency Alerts</label>
                      <p className="text-xs text-gray-500">Show prominent alerts for emergency patients</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.showEmergencyAlerts}
                      onChange={(e) => setSettings({...settings, showEmergencyAlerts: e.target.checked})}
                      className="form-checkbox h-4 w-4 text-blue-600"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Display Settings */}
            {activeTab === 'display' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-800">Display Settings</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                    <Select
                      value={settings.theme}
                      onChange={(e) => setSettings({...settings, theme: e.target.value as 'light' | 'dark' | 'auto'})}
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto (System)</option>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Default View</label>
                    <Select
                      value={settings.defaultView}
                      onChange={(e) => setSettings({...settings, defaultView: e.target.value as 'overview' | 'detailed' | 'statistics'})}
                    >
                      <option value="overview">Overview</option>
                      <option value="detailed">Detailed</option>
                      <option value="statistics">Statistics</option>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {/* Priority Settings */}
            {activeTab === 'priorities' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-800">Priority Weight Settings</h3>
                <p className="text-sm text-gray-600">Adjust wait time multipliers for different priority levels</p>
                
                <div className="space-y-4">
                  {Object.entries(settings.priorityWeights).map(([priority, weight]) => (
                    <div key={priority} className="flex items-center justify-between">
                      <label className="text-sm font-medium text-gray-700 capitalize">
                        {priority} Priority
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0.1"
                          max="2.0"
                          step="0.1"
                          value={weight}
                          onChange={(e) => setSettings({
                            ...settings,
                            priorityWeights: {
                              ...settings.priorityWeights,
                              [priority]: Number(e.target.value)
                            }
                          })}
                          className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                        />
                        <span className="text-xs text-gray-500">×</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Service Time Settings */}
            {activeTab === 'services' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-800">Default Service Times</h3>
                <p className="text-sm text-gray-600">Set average service times for wait time calculations</p>
                
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(settings.serviceTimeDefaults).map(([service, time]) => (
                    <div key={service} className="flex items-center justify-between">
                      <label className="text-sm font-medium text-gray-700 capitalize">
                        {service}
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="1"
                          max="120"
                          value={time}
                          onChange={(e) => setSettings({
                            ...settings,
                            serviceTimeDefaults: {
                              ...settings.serviceTimeDefaults,
                              [service]: Number(e.target.value)
                            }
                          })}
                          className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                        />
                        <span className="text-xs text-gray-500">min</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <Button
            onClick={handleReset}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-100"
          >
            <RotateCcw size={16} />
            <span>Reset to Default</span>
          </Button>
          
          <div className="flex items-center space-x-3">
            <Button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Save size={16} />
              <span>Save Settings</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
