import React, { useState } from "react";
import AbhaInput from "../../verification/AbhaInput";
import AadhaarInput from "../../verification/AadhaarInput";
import PanInput from "../../verification/PanInput";

type Props = {
    isEditMode: boolean;
};

export const PatientVerificationSection: React.FC<Props> = ({ isEditMode }) => {
    const [abhaNumber, setAbhaNumber] = useState("");
    const [aadhaarNumber, setAadhaarNumber] = useState("");
    const [panNumber, setPanNumber] = useState("");
    const [mode, setMode] = useState<"verify" | "generate">("verify");
    const [selectedMethod, setSelectedMethod] = useState<"aadhaar" | "pan">("aadhaar");

    if (isEditMode) return null;

    return (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                    <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                    <h3 className="text-lg font-semibold text-gray-800">
                        {mode === "verify" ? "Verify ABHA" : "Create ABHA"}
                    </h3>
                </div>
            </div>

            <div className="p-6">
                {mode === "verify" ? (
                    <>
                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1 text-gray-700">ABHA Number</label>
                            <AbhaInput abhaNumber={abhaNumber} setAbhaNumber={setAbhaNumber} />
                            <p className="mt-2 text-sm text-gray-600">
                                Use ABHA Number to verify existing patient records.
                            </p>
                        </div>
                        <button
                            onClick={() => setMode("generate")}
                            className="text-sm text-blue-600 underline hover:text-blue-800 transition"
                        >
                            Don’t have an ABHA Number? Generate one
                        </button>
                    </>
                ) : (
                    <>
                        <h4 className="text-md font-medium text-gray-800 mb-4">Generate ABHA Number Using:</h4>

                        <div className="flex flex-col md:flex-row md:items-start gap-6 mb-4">
                            <div className="w-full md:w-64">
                                <label className="block text-sm font-medium mb-1 text-gray-700">Select Method</label>
                                <select
                                    value={selectedMethod}
                                    onChange={(e) => setSelectedMethod(e.target.value as any)}
                                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="aadhaar">Aadhaar</option>
                                    <option value="pan">PAN (Coming Soon)</option>
                                </select>
                            </div>

                            {/* Aadhaar Input */}
                            {selectedMethod === "aadhaar" && (
                                <div className="flex-1">
                                    <label className="block text-sm font-medium mb-1 text-gray-700">Aadhaar Details</label>
                                    <AadhaarInput
                                        aadhaarNumber={aadhaarNumber}
                                        setAadhaarNumber={setAadhaarNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use Aadhaar to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}

                            {/* PAN Input */}
                            {selectedMethod === "pan" && (
                                <div className="flex-1">
                                    <label className="block text-sm font-medium mb-1 text-gray-700">PAN Details</label>
                                    <PanInput
                                        panNumber={panNumber}
                                        setPanNumber={setPanNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use PAN to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}
                        </div>

                        <button
                            onClick={() => setMode("verify")}
                            className="text-sm text-blue-600 underline hover:text-blue-800 transition"
                        >
                            Already have an ABHA Number? Verify here
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};
