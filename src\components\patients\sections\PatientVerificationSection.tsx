import React, { useState } from "react";
import AbhaInput from "../../verification/AbhaInput";
import AadhaarInput from "../../verification/AadhaarInput";
import PanInput from "../../verification/PanInput";

type Props = {
    isEditMode: boolean;
};

export const PatientVerificationSection: React.FC<Props> = ({ isEditMode }) => {
    const [abhaNumber, setAbhaNumber] = useState("");
    const [aadhaarNumber, setAadhaarNumber] = useState("");
    const [panNumber, setPanNumber] = useState("");
    const [mode, setMode] = useState<"verify" | "generate">("verify");
    const [selectedMethod, setSelectedMethod] = useState<"aadhaar" | "pan">("aadhaar");

    if (isEditMode) return null;

    return (
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-2">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-2 py-2 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                    <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                    <h3 className="text-lg font-semibold text-gray-800">
                        {mode === "verify" ? "Verify ABHA" : "Create ABHA"}
                    </h3>
                </div>
            </div>

            <div className="p-2">
                {mode === "verify" ? (
                    <>
                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1 text-gray-700">Verify ABHA using ABHA NUMBER or MOBILE NUMBER</label>
                            <AbhaInput abhaNumber={abhaNumber} setAbhaNumber={setAbhaNumber} />
                            {/* <p className="mt-2 text-sm text-gray-600">
                                Use ABHA Number to verify existing patient records.
                            </p> */}
                        </div>
                        <button
                            onClick={() => setMode("generate")}
                            className="text-sm font-semibold text-blue-600 underline hover:text-blue-800 transition cursor-pointer hover:underline hover:scale-105"
                        >
                            Don’t have an ABHA Number? Generate one
                        </button>
                    </>
                ) : (
                    <>
                        <h4 className="text-md font-medium text-gray-800 mb-4">Generate ABHA Number Using:</h4>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                            <div className="space-y-2">
                                <label className="block text-xs font-medium text-gray-700 mb-1">Select Method</label>
                                <select
                                    value={selectedMethod}
                                    onChange={(e) => setSelectedMethod(e.target.value as any)}
                                    className="w-full h-10 border border-gray-300 rounded-md px-3 py-2 text-sm bg-white text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                                >
                                    <option value="aadhaar">Aadhaar</option>
                                    <option value="pan">Driving License (Coming Soon)</option>
                                </select>
                            </div>

                            {/* Aadhaar Input */}
                            {selectedMethod === "aadhaar" && (
                                <div className="space-y-2">
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Aadhaar Details</label>
                                    <AadhaarInput
                                        aadhaarNumber={aadhaarNumber}
                                        setAadhaarNumber={setAadhaarNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use Aadhaar to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}

                            {/* PAN Input */}
                            {selectedMethod === "pan" && (
                                <div className="space-y-2">
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Enter Driving License Number</label>
                                    <PanInput
                                        panNumber={panNumber}
                                        setPanNumber={setPanNumber}
                                    />
                                    <p className="mt-2 text-sm text-gray-600">
                                        Use PAN to generate OTP and create a new ABHA profile.
                                    </p>
                                </div>
                            )}
                        </div>

                        <button
                            onClick={() => setMode("verify")}
                            className="text-sm font-semibold text-blue-600 underline hover:text-blue-800 transition cursor-pointer hover:underline hover:scale-105"
                        >
                            Already have an ABHA Number? Verify here
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};
