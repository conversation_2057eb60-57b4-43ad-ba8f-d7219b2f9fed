# Minimalist Waiting Room Display

## Overview
A clean, simple, and minimalist waiting room display that shows today's available doctors with their operating hours and respective departments. This component provides an easy-to-read interface for patients and visitors to quickly find available healthcare providers.

## Features

### ✨ **Key Highlights**
- **Clean Design**: Minimalist interface with focus on essential information
- **Real-time Clock**: Live updating date and time display
- **Department Organization**: Doctors grouped by their respective departments
- **Status Indicators**: Visual status indicators for doctor availability
- **Operating Hours**: Clear display of each doctor's working hours
- **Room Information**: Location details for each healthcare provider
- **Responsive Layout**: Works seamlessly across all device sizes

### 📊 **Summary Statistics**
- **Total Doctors**: Count of all healthcare providers
- **Available Now**: Number of currently available doctors
- **Departments**: Total number of medical departments
- **24/7 Services**: Emergency and round-the-clock services

### 🏥 **Department Information**
Each department card displays:
- Department name and code
- Number of available doctors
- Individual doctor details
- Operating hours and room locations
- Real-time availability status

## Mock Data Structure

### Doctor Information
```typescript
interface Doctor {
  id: string;
  name: string;
  department: string;
  specialization: string;
  startTime: string;        // Format: "HH:MM"
  endTime: string;          // Format: "HH:MM"
  isAvailable: boolean;
  status: 'Available' | 'Busy' | 'Break' | 'Offline';
  room?: string;
}
```

### Department Structure
```typescript
interface Department {
  id: string;
  name: string;
  code: string;
  color: string;           // Tailwind CSS color class
  doctors: Doctor[];
}
```

## Sample Departments & Doctors

### 🫀 **Cardiology (CARD)**
- **Dr. Rajesh Kumar** - Interventional Cardiologist
  - Hours: 9:00 AM - 5:00 PM
  - Room: 201
  - Status: Available

- **Dr. Priya Sharma** - Cardiac Surgeon
  - Hours: 10:00 AM - 4:00 PM
  - Room: 202
  - Status: Busy

### 🦴 **Orthopedics (ORTH)**
- **Dr. Amit Singh** - Joint Replacement Specialist
  - Hours: 8:30 AM - 4:30 PM
  - Room: 301
  - Status: Available

- **Dr. Neha Gupta** - Sports Medicine
  - Hours: 11:00 AM - 6:00 PM
  - Room: 302
  - Status: Break

### 👶 **Pediatrics (PEDS)**
- **Dr. Sunita Patel** - Child Development
  - Hours: 9:00 AM - 5:00 PM
  - Room: 101
  - Status: Available

- **Dr. Ravi Mehta** - Pediatric Surgery
  - Hours: 2:00 PM - 8:00 PM
  - Room: 102
  - Status: Offline

### 🧠 **Neurology (NEUR)**
- **Dr. Vikram Joshi** - Neurologist
  - Hours: 10:00 AM - 6:00 PM
  - Room: 401
  - Status: Available

### 🚨 **Emergency Medicine (EMRG)**
- **Dr. Anita Roy** - Emergency Physician
  - Hours: 24/7 Available
  - Room: Emergency Ward
  - Status: Available

- **Dr. Suresh Kumar** - Trauma Specialist
  - Hours: 8:00 AM - 8:00 PM
  - Room: Trauma Bay 1
  - Status: Busy

### 👩‍⚕️ **Gynecology (GYNE)**
- **Dr. Kavita Agarwal** - Obstetrician & Gynecologist
  - Hours: 9:30 AM - 5:30 PM
  - Room: 501
  - Status: Available

## Status Indicators

### 🟢 **Available**
- Doctor is currently available for consultations
- Green indicator and badge
- Patients can approach for immediate consultation

### 🟡 **Busy**
- Doctor is currently with a patient
- Yellow indicator and badge
- Patients may need to wait

### 🟠 **Break**
- Doctor is on a scheduled break
- Orange indicator and badge
- Will return shortly

### ⚫ **Offline**
- Doctor is not available today
- Gray indicator and badge
- Not accepting patients

## Design System

### 🎨 **Color Scheme**
- **Cardiology**: Red (`bg-red-500`)
- **Orthopedics**: Blue (`bg-blue-500`)
- **Pediatrics**: Green (`bg-green-500`)
- **Neurology**: Purple (`bg-purple-500`)
- **Emergency**: Dark Red (`bg-red-600`)
- **Gynecology**: Pink (`bg-pink-500`)

### 📱 **Responsive Design**
- **Mobile**: Single column layout
- **Tablet**: Two column grid
- **Desktop**: Optimized multi-column layout

### ⏰ **Time Format**
- 12-hour format with AM/PM
- Special handling for 24/7 services
- Real-time updates every second

## Usage

### Basic Implementation
```tsx
import { MinimalistWaitingRoom } from '../components/waiting-room/MinimalistWaitingRoom';

const WaitingRoomPage = () => {
  return <MinimalistWaitingRoom />;
};
```

### Integration with Main Display
```tsx
import WaitingRoomDisplay from '../pages/WaitingRoomDisplay';

const App = () => {
  return (
    <WaitingRoomDisplay 
      facilityId="facility-123"
      displayMode="minimalist"
    />
  );
};
```

## Customization Options

### Adding New Departments
1. Add department to `mockDepartments` array
2. Include department color class
3. Add doctors with their details
4. Update department code

### Modifying Doctor Information
1. Update doctor details in mock data
2. Adjust operating hours format
3. Modify room assignments
4. Update specializations

### Styling Customization
1. Modify Tailwind CSS classes
2. Adjust color schemes
3. Update spacing and typography
4. Customize card layouts

## Performance Features

### ⚡ **Optimizations**
- Efficient re-rendering with React hooks
- Minimal state updates
- Optimized component structure
- Responsive image handling

### 🔄 **Real-time Updates**
- Live clock updates every second
- Automatic status refresh capability
- Smooth transitions and animations

## Accessibility

### ♿ **Features**
- High contrast color combinations
- Clear typography hierarchy
- Semantic HTML structure
- Screen reader friendly
- Keyboard navigation support

## Future Enhancements

### 🚀 **Planned Features**
1. **API Integration**: Connect to real hospital management system
2. **Queue Information**: Display current queue lengths
3. **Appointment Booking**: Direct booking from display
4. **Multi-language Support**: Localization capabilities
5. **Voice Announcements**: Audio updates for accessibility
6. **Digital Signage**: Full-screen kiosk mode
7. **Patient Notifications**: SMS/Email integration
8. **Analytics Dashboard**: Usage statistics and insights

### 🔧 **Technical Improvements**
1. **WebSocket Integration**: Real-time data updates
2. **Offline Support**: PWA capabilities
3. **Performance Monitoring**: Analytics integration
4. **Error Handling**: Robust error management
5. **Testing Suite**: Comprehensive test coverage

## Deployment

### 📦 **Build Process**
```bash
npm run build
```

### 🌐 **Environment Setup**
- Configure API endpoints
- Set up real-time data sources
- Customize branding and colors
- Deploy to web servers or kiosks

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Compatibility**: React 18+, TypeScript 4.5+  
**License**: MIT
