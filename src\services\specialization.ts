// import axios from "axios";

// const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// export type Specialization = {
//   specializationId: string;
//   name: string;
//   code: string;
//   isClinical: boolean;
//   description: string;
// };

// export const getAllSpecializations = async (): Promise<Specialization[]> => {
//   try {
//     const response = await axios.get(`${BASE_URL}/api/specializations`);
//     return response.data;
//   } catch (error: any) {
//     console.error("Error fetching specializations:", error.response?.data || error.message);
//     return [];
//   }
// };
import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export type Department = {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string;
  description: string;
  phoneNumber: string;
  email: string;
  location: string;
  operatingHours: any[];  // keep as any[] or more specific type if you want
  isActive: boolean;
  isEmergencyDepartment: boolean;
};

export const getAllDepartments = async (): Promise<Department[]> => {
  try {
    const response = await axios.get(`${BASE_URL}/api/departments`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching departments:", error.response?.data || error.message);
    return [];
  }
};
