import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export type Specialization = {
  specializationId: string;
  name: string;
  code: string;
  isClinical: boolean;
  description: string;
};

export const getAllSpecializations = async (): Promise<Specialization[]> => {
  try {
    const response = await axios.get(`${BASE_URL}/api/specializations`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching specializations:", error.response?.data || error.message);
    return [];
  }
};
