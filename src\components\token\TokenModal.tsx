import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import type{ Specialization } from "../../services/specialization";
import { getAllSpecializations } from "../../services/specialization";

import { toast } from "react-toastify";

type TokenModalProps = {
    isOpen: boolean;
    onClose: () => void;
    patient: {
        patient_id: string;
        name: string;
        mobile: string;
        date_time: string;
    };
};

export const TokenModal: React.FC<TokenModalProps> = ({ isOpen, onClose, patient }) => {
    const navigate = useNavigate();

    const [departments, setDepartments] = useState<Specialization[]>([]);
    const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>("");
    const [qrBase64, setQrBase64] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [hasTriedSubmit, setHasTriedSubmit] = useState(false);


    // Fetch departments on mount
    useEffect(() => {
        const fetchDepartments = async () => {
            const data: Specialization[] = await getAllSpecializations();
            setDepartments(data);
            // default selecting first department
            // const firstClinical = data.find((d) => d.isClinical);
            // setSelectedDepartmentId(firstClinical ? firstClinical.specializationId : data[0]?.specializationId || "");
        };

        fetchDepartments();
    }, []);

    const handleGenerateToken = async () => {
        setHasTriedSubmit(true);
        if (!selectedDepartmentId) {
            toast.error("Please select a department");
            return;
        }
        setIsLoading(true);
        try {
            const res = await fetch("http://localhost:5000/register", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    patient_id: patient.patient_id,
                    name: patient.name,
                    department_id: selectedDepartmentId,
                    date_time: patient.date_time,
                }),
            });

            const result = await res.json();
            if (res.status === 201 && result.qr_card_base64) {
                setQrBase64(result.qr_card_base64);
            } else {
                toast.error("Token creation failed.");
            }
        } catch (err) {
            toast.error("Error generating token:", err);
            alert("Error occurred.");
        } finally {
            setIsLoading(false);
        }
    };


    const handleClose = () => {
        onClose();
        navigate("/list");
    };

    const handleDone = () => {
        onClose();
        navigate("/list");
    };

    const handleDownloadQR = () => {
        if (!qrBase64) return;
        const link = document.createElement("a");
        link.href = `data:image/png;base64,${qrBase64}`;
        link.download = `${patient.patient_id}_qr.png`;
        link.click();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md overflow-hidden">
                <div className="p-6 space-y-4 relative">

                    {/* Close (X) button top right */}
                    <button
                        onClick={handleClose}
                        className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl font-bold"
                        aria-label="Close modal"
                    >
                        &times;
                    </button>

                    <h3 className="text-lg font-medium text-blue-700 dark:text-blue-400 mb-4">Generate Patient Token</h3>

                    {/* Patient Info - Only including fields used in the request body */}
                    <div className="space-y-3">
                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Patient ID
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.patient_id}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Patient Name
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.name}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Mobile Number
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.mobile}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Date & Time
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.date_time}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>
                    </div>

                    {/* Department Selector */}
                    <div className="form-control w-full">
                        <label className="label">
                            <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                Department
                            </span>
                        </label>
                        <select
                            value={selectedDepartmentId}
                            onChange={(e) => setSelectedDepartmentId(e.target.value)}
                            className={`w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border ${hasTriedSubmit && !selectedDepartmentId
                                    ? "border-red-500"
                                    : "border-gray-300 dark:border-gray-600"
                                }`} disabled={!!qrBase64}
                        >
                            <option value="">Select Department</option>
                            {departments.map((dept) => (
                                <option key={dept.specializationId} value={dept.specializationId}>
                                    {dept.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Generate Token Button */}
                    <div className="flex justify-center pt-2">
                        <button
                            onClick={handleGenerateToken}
                            // disabled={!selectedDepartmentId || isLoading || !!qrBase64}
                            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded text-sm font-medium disabled:opacity-50"
                        >
                            Generate Token
                        </button>
                    </div>

                    {/* QR Code Display */}
                    {qrBase64 && (
                        <div className="flex flex-col items-center justify-center pt-2">
                            <img
                                src={`data:image/png;base64,${qrBase64}`}
                                alt="QR Code"
                                className="w-32 h-32 object-contain"
                            />
                            <div className="flex space-x-2 mt-3">
                                <button
                                    onClick={handleDownloadQR}
                                    className="bg-green-600 hover:bg-green-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Download QR
                                </button>
                                <button
                                    onClick={handleDone}
                                    className="bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Done
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
