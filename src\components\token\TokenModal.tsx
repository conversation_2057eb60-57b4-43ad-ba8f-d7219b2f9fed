import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import type { Specialization } from "../../services/specialization";
import { getAllSpecializations } from "../../services/specialization";
import { registerToken } from "../../services/tokenService";

import { toast } from "react-toastify";

type TokenModalProps = {
    isOpen: boolean;
    onClose: () => void;
    patient: {
        patient_id: string;
        name: string;
        mobile: string;
        date_time: string;
    };
};

export const TokenModal: React.FC<TokenModalProps> = ({ isOpen, onClose, patient }) => {
    const navigate = useNavigate();

    const [departments, setDepartments] = useState<Specialization[]>([]);
    const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>("");
    const [qrBase64, setQrBase64] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [hasTriedSubmit, setHasTriedSubmit] = useState(false);

    const getDepartmentNameById = (id: string) => {
        return departments.find((d) => d.specializationId === id)?.name || id;
    };



    // Fetch departments on mount
    useEffect(() => {
        const fetchDepartments = async () => {
            const data: Specialization[] = await getAllSpecializations();
            setDepartments(data);
            // default selecting first department
            // const firstClinical = data.find((d) => d.isClinical);
            // setSelectedDepartmentId(firstClinical ? firstClinical.specializationId : data[0]?.specializationId || "");
        };

        fetchDepartments();
    }, []);

    const handleGenerateToken = async () => {
        setHasTriedSubmit(true);
        if (!selectedDepartmentId) {
            toast.error("Please select a department");
            return;
        }
        setIsLoading(true);
        try {
            const response = await registerToken({
                patient_id: patient.patient_id,
                name: patient.name,
                department_id: selectedDepartmentId,
                department_name: getDepartmentNameById(selectedDepartmentId),
                date_time: patient.date_time,
            });

            if (response && response.qr_card_base64) {
                setQrBase64(response.qr_card_base64);
            } else {
                toast.error("Token creation failed.");
            }
        } catch (err) {
            toast.error("Error generating token:");
            console.log(err)
        } finally {
            setIsLoading(false);
        }
    };


    const handleClose = () => {
        onClose();
        navigate("/list");
    };

    const handleDone = () => {
        onClose();
        navigate("/list");
    };

    const handleDownloadQR = () => {
        if (!qrBase64) return;
        const link = document.createElement("a");
        link.href = `data:image/png;base64,${qrBase64}`;
        link.download = `${patient.patient_id}_qr.png`;
        link.click();
    };

    // Add this function inside your component:
    const handlePrintQR = () => {
        if (!qrBase64) return;

        const printWindow = window.open("", "_blank");
        if (!printWindow) {
            toast.error("Unable to open print window");
            return;
        }

        printWindow.document.write(`
    <html>
      <head>
        <title>Print QR</title>
        <style>
          @page {
            size: 3in 3in;
            margin: 0;
          }
          html, body {
            width: 3in;
            height: 3in;
            margin: 0;
            padding: 0;
          }
          img {
            display: block;
            width: 3in;
            height: 3in;
            margin: 0;
            padding: 0;
            border: none;
          }
        </style>
      </head>
      <body>
        <img src="data:image/png;base64,${qrBase64}" alt="QR Code" />
        <script>
          window.onload = function() {
            window.print();
            window.close();
          }
        </script>
      </body>
    </html>
  `);

        printWindow.document.close();
    };


    const handlePrintPrescription = () => {
    if (!qrBase64) return;

    const departmentName = getDepartmentNameById(selectedDepartmentId);
    const printWindow = window.open("", "_blank");
    if (!printWindow) {
        toast.error("Unable to open print window");
        return;
    }

    printWindow.document.write(`
      <html>
        <head>
          <title>Prescription</title>
          <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
          <style>
            @media print {
              body {
                margin: 0;
                padding: 0;
              }
            }
          </style>
        </head>
        <body class="min-h-screen flex flex-col p-8 font-sans bg-white text-black">
          <!-- Top Section -->
          <div>
            <div class="text-center mb-4">
              <h1 class="text-3xl font-bold">Meghasanjivini</h1>
            </div>

            <hr class="border-gray-400 mb-4"/>

            <div class="flex justify-between items-start border-b pb-4 mb-6">
              <div class="text-left space-y-1 text-sm">
                <p><strong>Department:</strong> ${departmentName}</p>
                <p><strong>Patient Name:</strong> ${patient.name}</p>
                <p><strong>Patient ID:</strong> ${patient.patient_id}</p>
                <p><strong>Mobile:</strong> ${patient.mobile}</p>
                <p><strong>Date & Time:</strong> ${patient.date_time}</p>
              </div>
              <div class="w-24 h-24">
                <img src="data:image/png;base64,${qrBase64}" class="w-full h-full object-contain" />
              </div>
            </div>

            <hr class="border-gray-300 my-4"/>
          </div>

          <!-- Notes Section (Flexible height) -->
          <div class="flex-1 mb-12">
            <h2 class="text-base font-semibold mb-2">Doctor's Notes:</h2>
            <div class="border h-full p-2 font-mono text-sm bg-gray-50 w-full"></div>
          </div>

          <!-- Bottom Signature -->
          <div class="text-right">
            <p class="italic">Doctor's Signature</p>
          </div>

          <script>
            window.onload = function () {
              window.print();
              window.close();
            };
          </script>
        </body>
      </html>
    `);

    printWindow.document.close();
};




    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md overflow-hidden">
                <div className="p-6 space-y-4 relative">

                    {/* Close (X) button top right */}
                    <button
                        onClick={handleClose}
                        className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl font-bold"
                        aria-label="Close modal"
                    >
                        &times;
                    </button>

                    <h3 className="text-lg font-medium text-blue-700 dark:text-blue-400 mb-4">Generate Patient Token</h3>

                    {/* Patient Info - Only including fields used in the request body */}
                    <div className="space-y-3">
                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Patient ID
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.patient_id}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Patient Name
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.name}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Mobile Number
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.mobile}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>

                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                    Date & Time
                                </span>
                            </label>
                            <input
                                readOnly
                                value={patient.date_time}
                                className="w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600"
                            />
                        </div>
                    </div>

                    {/* Department Selector */}
                    <div className="form-control w-full">
                        <label className="label">
                            <span className="label-text text-sm font-medium text-blue-700 dark:text-blue-400">
                                Department
                            </span>
                        </label>
                        <select
                            value={selectedDepartmentId}
                            onChange={(e) => setSelectedDepartmentId(e.target.value)}
                            className={`w-full px-3 py-2 text-sm rounded bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border ${hasTriedSubmit && !selectedDepartmentId
                                ? "border-red-500"
                                : "border-gray-300 dark:border-gray-600"
                                }`} disabled={!!qrBase64}
                        >
                            <option value="">Select Department</option>
                            {departments.map((dept) => (
                                <option key={dept.specializationId} value={dept.specializationId}>
                                    {dept.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Generate Token Button */}
                    <div className="flex justify-center pt-2">
                        <button
                            onClick={handleGenerateToken}
                            // disabled={!selectedDepartmentId || isLoading || !!qrBase64}
                            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded text-sm font-medium disabled:opacity-50"
                        >
                            Generate Token
                        </button>
                    </div>

                    {/* QR Code Display */}
                    {qrBase64 && (
                        <div className="flex flex-col items-center justify-center pt-2">
                            <img
                                src={`data:image/png;base64,${qrBase64}`}
                                alt="QR Code"
                                className="w-32 h-32 object-contain"
                            />
                            <div className="flex space-x-2 mt-3">
                                <button
                                    onClick={handleDownloadQR}
                                    className="bg-green-600 hover:bg-green-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Download QR
                                </button>
                                <button
                                    onClick={handlePrintQR}
                                    className="bg-yellow-600 hover:bg-yellow-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Print QR
                                </button>
                                <button
                                    onClick={handlePrintPrescription}
                                    className="bg-purple-600 hover:bg-purple-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Print Prescription
                                </button>
                                <button
                                    onClick={handleDone}
                                    className="bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-4 rounded text-sm"
                                >
                                    Done
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
