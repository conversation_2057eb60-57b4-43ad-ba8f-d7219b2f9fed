import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, Clock, User, MapPin, MoreHorizontal } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { Select } from '../../commonfields/Select';
import { getAppointments } from '../../services/appointmentApis';
import { getProviders } from '../../services/providerApis';
import type { Appointment } from '../../types/appointment';
import type { Provider } from '../../types/provider';
import { showError } from '../../utils/toastUtils';

interface AppointmentsListProps {
  onAppointmentSelect?: (appointment: Appointment) => void;
  onBookNew?: () => void;
}

export const AppointmentsList: React.FC<AppointmentsListProps> = ({
  onAppointmentSelect,
  onBookNew
}) => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<'upcoming' | 'completed' | 'cancelled'>('upcoming');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedVisitType, setSelectedVisitType] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    loadAppointments();
    loadProviders();
  }, [selectedTab]);

  const loadAppointments = async () => {
    setLoading(true);
    try {
      const response = await getAppointments({
        status: selectedTab === 'upcoming' ? 'Scheduled,Confirmed' : 
               selectedTab === 'completed' ? 'Completed' : 'Cancelled',
        size: 100
      });
      setAppointments(response.results || []);
    } catch (error) {
      console.error('Failed to load appointments:', error);
      showError('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  const loadProviders = async () => {
    try {
      const response = await getProviders({ isActive: true, size: 100 });
      setProviders(response.results || []);
    } catch (error) {
      console.error('Failed to load providers:', error);
    }
  };

  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = 
      appointment.patient?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.patient?.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.provider?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.provider?.lastName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesProvider = !selectedProvider || appointment.providerId === selectedProvider;
    const matchesVisitType = !selectedVisitType || appointment.type === selectedVisitType;
    
    return matchesSearch && matchesProvider && matchesVisitType;
  });

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      'Scheduled': 'bg-blue-100 text-blue-800',
      'Confirmed': 'bg-green-100 text-green-800',
      'Completed': 'bg-gray-100 text-gray-800',
      'Cancelled': 'bg-red-100 text-red-800',
      'In Progress': 'bg-yellow-100 text-yellow-800'
    };
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    if (timeString.includes('T')) {
      return new Date(timeString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    }
    return timeString;
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Appointments</h1>
        </div>
        <Button
          onClick={onBookNew}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium"
        >
          Book New Appointment
        </Button>
      </div>

      {/* Tabs and Filters */}
      <div className="p-6 border-b border-gray-200">
        {/* Tabs */}
        <div className="flex space-x-8 mb-6">
          <button
            onClick={() => setSelectedTab('upcoming')}
            className={`pb-2 border-b-2 font-medium text-sm ${
              selectedTab === 'upcoming'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Upcoming Appointments
          </button>
          <button
            onClick={() => setSelectedTab('completed')}
            className={`pb-2 border-b-2 font-medium text-sm ${
              selectedTab === 'completed'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Completed Appointments
          </button>
          <button
            onClick={() => setSelectedTab('cancelled')}
            className={`pb-2 border-b-2 font-medium text-sm ${
              selectedTab === 'cancelled'
                ? 'border-purple-600 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Cancelled Appointments
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <Input
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64 rounded-lg"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Filter Tags */}
            <div className="flex items-center space-x-2">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                Consultation
              </span>
              <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                Follow-up
              </span>
              <span className="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full">
                Emergency
              </span>
            </div>
            
            <Button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter size={16} />
              <span>Filter</span>
            </Button>
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Doctor</label>
                <Select
                  value={selectedProvider}
                  onChange={(e) => setSelectedProvider(e.target.value)}
                  className="rounded-lg"
                >
                  <option value="">All Doctors</option>
                  {providers.map((provider) => (
                    <option key={provider.providerId} value={provider.providerId}>
                      {provider.title} {provider.firstName} {provider.lastName}
                    </option>
                  ))}
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <Select className="rounded-lg">
                  <option value="">All Departments</option>
                  <option value="cardiology">Cardiology</option>
                  <option value="pediatrics">Pediatrics</option>
                  <option value="emergency">Emergency Medicine</option>
                  <option value="orthopedics">Orthopedics</option>
                  <option value="neurology">Neurology</option>
                  <option value="oncology">Oncology</option>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Visit Type</label>
                <Select
                  value={selectedVisitType}
                  onChange={(e) => setSelectedVisitType(e.target.value)}
                  className="rounded-lg"
                >
                  <option value="">All Types</option>
                  <option value="Consultation">Consultation</option>
                  <option value="Follow-up">Follow-up</option>
                  <option value="Emergency">Emergency</option>
                </Select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Appointments Table */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Patient Name</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Doctor</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Department</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Visit Type</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-600"></th>
                </tr>
              </thead>
              <tbody>
                {filteredAppointments.map((appointment) => (
                  <tr
                    key={appointment.appointmentId}
                    className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => onAppointmentSelect?.(appointment)}
                  >
                    <td className="py-4 px-4">
                      <div className="text-sm font-medium text-gray-900">
                        {formatDate(appointment.appointmentDate)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatTime(appointment.startTime)}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm font-medium text-gray-900">
                        {appointment.patient?.firstName} {appointment.patient?.lastName}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm text-gray-900">
                        {appointment.provider?.title} {appointment.provider?.firstName} {appointment.provider?.lastName}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm text-gray-900">
                        {appointment.provider?.specialization || 'N/A'}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm text-gray-900">
                        {appointment.type}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      {getStatusBadge(appointment.status)}
                    </td>
                    <td className="py-4 px-4">
                      <Button className="p-1 hover:bg-gray-100 rounded">
                        <MoreHorizontal size={16} className="text-gray-400" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredAppointments.length === 0 && !loading && (
              <div className="text-center py-12">
                <p className="text-gray-500">No appointments found</p>
              </div>
            )}
          </div>
        )}
        
        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing {filteredAppointments.length} of {appointments.length} appointments
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">37 / 134</span>
            <Button className="px-3 py-1 border border-gray-300 rounded hover:bg-gray-50">
              Restart
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
