import { Routes, Route, Navigate, Outlet, useLocation } from "react-router-dom";
import Homepage from "../pages/Homepage";
import { Layout } from '../components/layouts/Layout';
import PatientPage from "../pages/PatientPage";
import PatientListPage from "../pages/PatientListPage";
import AbhaRegistrationForm from "../components/Abha/Aadhaar/AbhaRegistrationForm";
import AppointmentsPage from "../pages/AppointmentsPage";
import { QueueManagementPage } from "../pages/QueueManagementPage";
import ScheduleManagementPage from "../pages/ScheduleManagementPage";
import Login from "../authentication/Login";
import SignupPage from "../authentication/SignupPage";
import DoctorInterfaceWrapper from "../pages/DoctorInterfaceWrapper";
import WaitingRoomDisplay from "../pages/WaitingRoomDisplay";
import { DepartmentManagement } from "../components/departments/DepartmentManagement";
import { ProviderDepartmentMappingComponent } from "../components/departments/ProviderDepartmentMapping";


// Route guard for protected routes
const PrivateRoute = () => {
  const isAuthenticated = localStorage.getItem("loggedInUser");
  const location = useLocation();

  return isAuthenticated ? (
    <Outlet />
  ) : (
    <Navigate to="/login" replace state={{ from: location }} />
  );
};


export const AppRoutes = () => (
  <Routes>
    {/* Public login route */}
    <Route path="/login" element={<Login />} />
    <Route path="/signup" element={<SignupPage />} />

    {/* Protected application routes */}
    <Route element={<PrivateRoute />}>
      <Route path="/" element={<Layout />}>
        <Route index element={<Homepage />} />
        <Route path="patients" element={<PatientPage />} />
        <Route path="patients/:id" element={<PatientPage />} />
        <Route path="list" element={<PatientListPage />} />
        <Route path="abha-aadhaar" element={<AbhaRegistrationForm />} />
        <Route path="appointments" element={<AppointmentsPage />} />
        <Route path="schedules" element={<ScheduleManagementPage />} />
        <Route path="departments" element={<DepartmentManagement />} />
        <Route path="provider-mapping" element={<ProviderDepartmentMappingComponent />} />

      <Route path="queue" element={<QueueManagementPage />} />
      <Route path="doctor-interface" element={<DoctorInterfaceWrapper />} />
      </Route>
    </Route>
     <Route path="waiting-room" element={<WaitingRoomDisplay facilityId="1" />} />
    <Route path="waiting-room-compact" element={<WaitingRoomDisplay facilityId="1" displayMode="compact" />} />
    <Route path="waiting-room-department" element={<WaitingRoomDisplay facilityId="1" displayMode="department" />} />
  </Routes>
);
