// DoctorFormPage.tsx

import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getDoctorById, createDoctor, updateDoctor } from "../../services/doctorApis";
import { getDepartments } from "../../services/departmentApis";
import type { DoctorDTO } from "../../services/doctorApis";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Department {
  departmentId: string;
  name: string;
}

const DoctorFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);

  const [departments, setDepartments] = useState<Department[]>([]);
  const [form, setForm] = useState<
    Omit<DoctorDTO, "languagesSpoken" | "createdAt"> & {
      languagesSpoken: string;
    }
  >({
    fullName: "",
    registrationNumber: "",
    registrationState: "",
    yearsOfExperience: 0,
    telemedicineReady: false,
    languagesSpoken: "",
    isActive: true,
    specialization: "",
  });

  useEffect(() => {
    const fetchDepartments = async () => {
      const res = await getDepartments();
      if (res.success) {
        const extracted = (res.data.results || []).map((d: any) => ({
          departmentId: d.id,
          name: d.name,
        }));
        setDepartments(extracted);
      } else {
        toast.error("Failed to load departments.");
      }
    };

    const fetchDoctor = async () => {
      if (!isEdit) return;
      const response = await getDoctorById(id!);
      if (response.success) {
        const d = response.data;
        setForm({
          fullName: d.fullName,
          registrationNumber: d.registrationNumber,
          registrationState: d.registrationState,
          yearsOfExperience: d.yearsOfExperience,
          telemedicineReady: d.telemedicineReady,
          languagesSpoken: Array.isArray(d.languagesSpoken)
            ? d.languagesSpoken.join(", ")
            : d.languagesSpoken,
          isActive: d.isActive,
          specialization: d.specialization,
        });
      } else {
        toast.error("Failed to load doctor data.");
      }
    };

    fetchDepartments();
    fetchDoctor();
  }, [id, isEdit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target;
    const { name, value, type } = target;
    const newValue = type === "checkbox" && target instanceof HTMLInputElement
      ? target.checked
      : value;

    setForm({ ...form, [name]: newValue });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.specialization) {
      toast.error("Please select a specialization (department).");
      return;
    }

    const doctorPayload: DoctorDTO = {
      ...form,
      languagesSpoken: form.languagesSpoken
        .split(",")
        .map((lang) => lang.trim())
        .filter((lang) => lang.length > 0),
    };
console.log("doctor payload ",doctorPayload);
    const res = isEdit
      ? await updateDoctor(id!, doctorPayload)
      : await createDoctor(doctorPayload);

    if (res.success) {
      toast.success(`Doctor ${isEdit ? "updated" : "created"} successfully.`);
      navigate("/doctors");
    } else {
      toast.error(`Failed to ${isEdit ? "update" : "create"} doctor: ${res.error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-10 px-4">
      <ToastContainer />
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8 space-y-6">
        <h2 className="text-3xl font-bold text-indigo-700">
          {isEdit ? "Edit Doctor" : "Add New Doctor"}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Doctor Details */}
          <div className="p-6 rounded-xl bg-blue-50 border">
            <h3 className="text-xl font-semibold mb-4 text-blue-700">Doctor Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Full Name</label>
                <input
                  name="fullName"
                  value={form.fullName}
                  onChange={handleChange}
                  placeholder="Full Name"
                  className="border border-gray-300 p-3 rounded w-full"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Registration Number</label>
                <input
                  name="registrationNumber"
                  value={form.registrationNumber}
                  onChange={handleChange}
                  placeholder="Registration Number"
                  className="border border-gray-300 p-3 rounded w-full"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Registration State</label>
                <input
                  name="registrationState"
                  value={form.registrationState}
                  onChange={handleChange}
                  placeholder="Registration State"
                  className="border border-gray-300 p-3 rounded w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Years of Experience</label>
                <input
                  type="number"
                  name="yearsOfExperience"
                  value={form.yearsOfExperience}
                  onChange={handleChange}
                  placeholder="Years of Experience"
                  className="border border-gray-300 p-3 rounded w-full"
                />
              </div>
            </div>
          </div>

          {/* Professional Info */}
          <div className="p-6 rounded-xl bg-purple-50 border">
            <h3 className="text-xl font-semibold mb-4 text-purple-700">Professional Info</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-1">Languages Spoken</label>
                <input
                  name="languagesSpoken"
                  value={form.languagesSpoken}
                  onChange={handleChange}
                  placeholder="Languages (comma-separated)"
                  className="border border-gray-300 p-3 rounded w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Specialization / Department</label>
                <select
                  name="specialization"
                  value={form.specialization}
                  onChange={handleChange}
                  className="border border-gray-300 p-3 rounded w-full"
                  required
                >
                  <option value="">Select Department</option>
                  {departments.map((dept) => (
                    <option key={dept.departmentId} value={dept.departmentId}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Status */}
          <div className="p-6 rounded-xl bg-gray-50 border">
            <h3 className="text-xl font-semibold mb-4 text-gray-700">Availability</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <label className="flex items-center space-x-3 text-gray-700">
                <input
                  type="checkbox"
                  name="telemedicineReady"
                  checked={form.telemedicineReady}
                  onChange={handleChange}
                />
                <span>Telemedicine Ready</span>
              </label>
              <label className="flex items-center space-x-3 text-gray-700">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={form.isActive}
                  onChange={handleChange}
                />
                <span>Active</span>
              </label>
            </div>
          </div>

          <div className="text-right">
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-xl"
            >
              {isEdit ? "Save Changes" : "Create Doctor"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DoctorFormPage;
