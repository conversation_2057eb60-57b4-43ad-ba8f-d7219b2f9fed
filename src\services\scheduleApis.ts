import axios from "axios";
import type {
  DoctorSchedule,
  ScheduleConfigPayload,
  SlotConfigResponse,
  SlotGenerationRequest,
  SlotGenerationResponse,
  AvailableSlot,
  ScheduleFilters,
  ScheduleStats,
  ScheduleValidation
} from '../types/schedule';

// Doctor API Response Types
export interface DoctorSpecialization {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string;
  description: string;
  phoneNumber: string;
  email: string;
  location: string;
  operatingHours: Array<{
    id: number;
    dayOfWeek: string;
    isOperating: boolean;
    startTime: string;
    endTime: string;
    breakStartTime?: string;
    breakEndTime?: string;
    emergencyHours: boolean;
    createdAt: string;
    updatedAt: string;
  }>;
  isActive: boolean;
  isEmergencyDepartment: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Doctor {
  doctorId: string;
  userId: string;
  specialization: DoctorSpecialization;
  fullName: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
  createdAt: string;
}

const BASE_URL = "https://megha-dev.sirobilt.com";

// Get All Doctors/Consultants
export const getDoctors = async (): Promise<{ success: boolean; data?: Doctor[]; error?: string }> => {
  try {
    console.log("Fetching doctors/consultants");

    const response = await axios.get("https://megha-dev.sirobilt.com/api/doctors");

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to fetch doctors:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch doctors"
    };
  }
};

// Create Doctor Schedule Configuration
export const createScheduleConfig = async (payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Creating schedule configuration:", payload);

    // Use new slot-configs endpoint
    const response = await axios.post(`${BASE_URL}/api/consultants/${payload.consultantId}/slot-configs`, payload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to create schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to create schedule configuration"
    };
  }
};

// Get All Schedule Configurations
export const getScheduleConfigs = async (filters: ScheduleFilters = {}): Promise<{
  results: DoctorSchedule[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}> => {
  try {
    console.log("Fetching schedule configurations with filters:", filters);



    // Use new slot-configs endpoint
    let endpoint = `${BASE_URL}/api/consultants/{consultantId}/slot-configs`;

    // // If consultantId is provided, use specific consultant endpoint
    // if (filters.consultantId) {
    //   endpoint = `${BASE_URL}/api/consultants/${filters.consultantId}/slot-configs`;
    // }

    const response = await axios.get(endpoint);

    // Transform response to match expected format
    const schedules = Array.isArray(response.data) ? response.data : [];

    return {
      results: schedules,
      totalElements: schedules.length,
      totalPages: 1,
      page: 0,
      size: schedules.length
    };
  } catch (error: any) {
    console.error("Failed to fetch schedule configurations:", error);
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20
    };
  }
};

// Update Schedule Configuration
export const updateScheduleConfig = async (consultantId: string, payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Updating schedule configuration for consultant:", consultantId, payload);

    // Use new slot-configs endpoint with PUT method
    const response = await axios.put(`${BASE_URL}/api/consultants/${consultantId}/slot-configs`, payload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to update schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to update schedule configuration"
    };
  }
};

// Get Schedule Configurations for Specific Consultant
export const getConsultantSlotConfigs = async (consultantId?: string): Promise<{ success: boolean; data?: SlotConfigResponse[]; error?: string }> => {
  try {
    console.log("Fetching slot configurations for consultant:", consultantId);



    // Use new slot-configs endpoint
    let endpoint = `${BASE_URL}/api/consultants/slot-configs`;

    // If consultantId is provided, use specific consultant endpoint
    if (consultantId) {
      endpoint = `${BASE_URL}/api/consultants/${consultantId}/slot-configs`;
    }

    const response = await axios.get(endpoint);

    return {
      success: true,
      data: Array.isArray(response.data) ? response.data : []
    };
  } catch (error: any) {
    console.error("Failed to fetch consultant slot configurations:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch slot configurations"
    };
  }
};

// Delete Schedule Configuration
export const deleteScheduleConfig = async (scheduleId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("Deleting schedule configuration:", scheduleId);

    await axios.delete(`${BASE_URL}/api/schedules/${scheduleId}`);

    return {
      success: true
    };
  } catch (error: any) {
    console.error("Failed to delete schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to delete schedule configuration"
    };
  }
};

// Generate Slots
export const generateSlots = async (request: SlotGenerationRequest): Promise<SlotGenerationResponse> => {
  try {
    console.log("Generating slots:", request);

    const response = await axios.post(`${BASE_URL}/api/schedules/generate-slots`, request);

    return response.data;
  } catch (error: any) {
    console.error("Failed to generate slots:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to generate slots",
      data: {
        totalSlotsGenerated: 0,
        dateRange: { from: request.from, to: request.to },
        providersAffected: [],
        conflicts: []
      }
    };
  }
};

// Get Available Slots for Provider and Date Range
export const getAvailableSlots = async (providerId: string, fromDate: string, toDate: string): Promise<AvailableSlot[]> => {
  try {
    console.log("Fetching available slots:", { providerId, fromDate, toDate });



    const params = new URLSearchParams();
    params.append('providerId', providerId);
    params.append('fromDate', fromDate);
    params.append('toDate', toDate);

    const response = await axios.get(`${BASE_URL}/api/schedules/available-slots?${params}`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch available slots:", error);
    return [];
  }
};

// Get Schedule Statistics
export const getScheduleStats = async (): Promise<ScheduleStats> => {
  try {
    console.log("Fetching schedule statistics");



    const response = await axios.get(`${BASE_URL}/api/schedules/stats`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule statistics:", error);
    return {
      totalSchedules: 0,
      activeSchedules: 0,
      providersWithSchedules: 0,
      totalSlotsGenerated: 0,
      availableSlots: 0,
      bookedSlots: 0,
      upcomingScheduleChanges: 0
    };
  }
};

// Validate Schedule Configuration
export const validateScheduleConfig = async (payload: ScheduleConfigPayload): Promise<ScheduleValidation> => {
  try {
    console.log("Validating schedule configuration:", payload);

    const response = await axios.post(`${BASE_URL}/api/schedules/validate`, payload);

    return response.data;
  } catch (error: any) {
    console.error("Failed to validate schedule configuration:", error);
    return {
      isValid: false,
      errors: ["Validation failed"],
      warnings: [],
      conflicts: []
    };
  }
};
