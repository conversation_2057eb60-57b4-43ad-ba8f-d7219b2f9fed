import axios from "axios";
import { DayOfWeek } from '../types/appointmentenums';
import type {
  DoctorSchedule,
  ScheduleConfigPayload,
  SlotGenerationRequest,
  SlotGenerationResponse,
  AvailableSlot,
  ScheduleFilters,
  ScheduleStats,
  ScheduleValidation,
  BulkScheduleOperation,
  ScheduleTemplate,
  ProviderScheduleSummary
} from '../types/schedule';

const BASE_URL = "https://megha-dev.sirobilt.com";
const USE_MOCK_DATA = true; // Set to false to use real API

// Mock data for development
const mockSchedules: DoctorSchedule[] = [
  {
    scheduleId: "sched-001",
    consultantId: "08756dae-86dc-4eaf-b1bb-c0d3faba7e39",
    daysOfWeek: [DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday],
    startTime: "09:00:00",
    endTime: "17:00:00",
    slotDuration: 30,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  },
  {
    scheduleId: "sched-002",
    consultantId: "prov-002",
    daysOfWeek: [DayOfWeek.Monday, DayOfWeek.Wednesday, DayOfWeek.Friday],
    startTime: "10:00:00",
    endTime: "16:00:00",
    slotDuration: 45,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  }
];

const mockAvailableSlots: AvailableSlot[] = [
  {
    slotId: "slot-001",
    providerId: "08756dae-86dc-4eaf-b1bb-c0d3faba7e39",
    date: "2025-06-19",
    startTime: "09:00:00",
    endTime: "09:30:00",
    duration: 30,
    isAvailable: true,
    isBooked: false
  },
  {
    slotId: "slot-002",
    providerId: "08756dae-86dc-4eaf-b1bb-c0d3faba7e39",
    date: "2025-06-19",
    startTime: "09:30:00",
    endTime: "10:00:00",
    duration: 30,
    isAvailable: false,
    isBooked: true,
    appointmentId: "apt-001"
  }
];

// Create Doctor Schedule Configuration
export const createScheduleConfig = async (payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Creating schedule configuration:", payload);

    if (USE_MOCK_DATA) {
      // Mock implementation
      const newSchedule: DoctorSchedule = {
        scheduleId: `sched-${Date.now()}`,
        consultantId: payload.consultantId,
        daysOfWeek: payload.daysOfWeek as any[],
        startTime: payload.startTime,
        endTime: payload.endTime,
        slotDuration: payload.slotDuration as any,
        effectiveFrom: payload.effectiveFrom,
        effectiveTo: payload.effectiveTo,
        isActive: true,
        createdAt: new Date().toISOString(),
        createdBy: "current-user"
      };

      return {
        success: true,
        data: newSchedule
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules`, payload);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to create schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to create schedule configuration"
    };
  }
};

// Get All Schedule Configurations
export const getScheduleConfigs = async (filters: ScheduleFilters = {}): Promise<{
  results: DoctorSchedule[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}> => {
  try {
    console.log("Fetching schedule configurations with filters:", filters);

    if (USE_MOCK_DATA) {
      // Mock implementation with filtering
      let filteredSchedules = [...mockSchedules];
      
      if (filters.consultantId) {
        filteredSchedules = filteredSchedules.filter(s => s.consultantId === filters.consultantId);
      }
      
      if (filters.isActive !== undefined) {
        filteredSchedules = filteredSchedules.filter(s => s.isActive === filters.isActive);
      }

      return {
        results: filteredSchedules,
        totalElements: filteredSchedules.length,
        totalPages: 1,
        page: 0,
        size: filteredSchedules.length
      };
    }

    const params = new URLSearchParams();
    if (filters.consultantId) params.append('consultantId', filters.consultantId);
    if (filters.dayOfWeek) params.append('dayOfWeek', filters.dayOfWeek);
    if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters.effectiveFrom) params.append('effectiveFrom', filters.effectiveFrom);
    if (filters.effectiveTo) params.append('effectiveTo', filters.effectiveTo);
    params.append('page', (filters.page || 0).toString());
    params.append('size', (filters.size || 20).toString());

    const response = await axios.get(`${BASE_URL}/api/schedules?${params}`);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule configurations:", error);
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20
    };
  }
};

// Update Schedule Configuration
export const updateScheduleConfig = async (scheduleId: string, payload: Partial<ScheduleConfigPayload>): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Updating schedule configuration:", scheduleId, payload);

    if (USE_MOCK_DATA) {
      // Mock implementation
      const existingSchedule = mockSchedules.find(s => s.scheduleId === scheduleId);
      if (!existingSchedule) {
        return {
          success: false,
          error: "Schedule not found"
        };
      }

      const updatedSchedule: DoctorSchedule = {
        ...existingSchedule,
        ...payload,
        daysOfWeek: payload.daysOfWeek ? payload.daysOfWeek.map(day => day as DayOfWeek) : existingSchedule.daysOfWeek,
        updatedAt: new Date().toISOString(),
        updatedBy: "current-user"
      };

      return {
        success: true,
        data: updatedSchedule
      };
    }

    const response = await axios.put(`${BASE_URL}/api/schedules/${scheduleId}`, payload);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to update schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to update schedule configuration"
    };
  }
};

// Delete Schedule Configuration
export const deleteScheduleConfig = async (scheduleId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("Deleting schedule configuration:", scheduleId);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true
      };
    }

    await axios.delete(`${BASE_URL}/api/schedules/${scheduleId}`);
    
    return {
      success: true
    };
  } catch (error: any) {
    console.error("Failed to delete schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to delete schedule configuration"
    };
  }
};

// Generate Slots
export const generateSlots = async (request: SlotGenerationRequest): Promise<SlotGenerationResponse> => {
  try {
    console.log("Generating slots:", request);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true,
        message: "Slots generated successfully",
        data: {
          totalSlotsGenerated: 150,
          dateRange: {
            from: request.from,
            to: request.to
          },
          providersAffected: request.providerId ? [request.providerId] : ["08756dae-86dc-4eaf-b1bb-c0d3faba7e39", "prov-002"],
          conflicts: []
        }
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/generate-slots`, request);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to generate slots:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to generate slots",
      data: {
        totalSlotsGenerated: 0,
        dateRange: { from: request.from, to: request.to },
        providersAffected: [],
        conflicts: []
      }
    };
  }
};

// Get Available Slots for Provider and Date Range
export const getAvailableSlots = async (providerId: string, fromDate: string, toDate: string): Promise<AvailableSlot[]> => {
  try {
    console.log("Fetching available slots:", { providerId, fromDate, toDate });

    if (USE_MOCK_DATA) {
      // Mock implementation - filter by provider and date range
      return mockAvailableSlots.filter(slot => 
        slot.providerId === providerId && 
        slot.date >= fromDate && 
        slot.date <= toDate
      );
    }

    const params = new URLSearchParams();
    params.append('providerId', providerId);
    params.append('fromDate', fromDate);
    params.append('toDate', toDate);

    const response = await axios.get(`${BASE_URL}/api/schedules/available-slots?${params}`);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch available slots:", error);
    return [];
  }
};

// Get Schedule Statistics
export const getScheduleStats = async (): Promise<ScheduleStats> => {
  try {
    console.log("Fetching schedule statistics");

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        totalSchedules: 25,
        activeSchedules: 22,
        providersWithSchedules: 15,
        totalSlotsGenerated: 1250,
        availableSlots: 890,
        bookedSlots: 360,
        upcomingScheduleChanges: 3
      };
    }

    const response = await axios.get(`${BASE_URL}/api/schedules/stats`);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule statistics:", error);
    return {
      totalSchedules: 0,
      activeSchedules: 0,
      providersWithSchedules: 0,
      totalSlotsGenerated: 0,
      availableSlots: 0,
      bookedSlots: 0,
      upcomingScheduleChanges: 0
    };
  }
};

// Validate Schedule Configuration
export const validateScheduleConfig = async (payload: ScheduleConfigPayload): Promise<ScheduleValidation> => {
  try {
    console.log("Validating schedule configuration:", payload);

    if (USE_MOCK_DATA) {
      // Mock validation
      const errors: string[] = [];
      const warnings: string[] = [];

      // Basic validation
      if (payload.startTime >= payload.endTime) {
        errors.push("End time must be after start time");
      }

      if (payload.daysOfWeek.length === 0) {
        errors.push("At least one day of week must be selected");
      }

      if (new Date(payload.effectiveFrom) >= new Date(payload.effectiveTo)) {
        errors.push("Effective to date must be after effective from date");
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        conflicts: []
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/validate`, payload);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to validate schedule configuration:", error);
    return {
      isValid: false,
      errors: ["Validation failed"],
      warnings: [],
      conflicts: []
    };
  }
};
