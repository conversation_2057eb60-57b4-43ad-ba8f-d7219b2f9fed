import axios from "axios";
import { DayOfWeek, SlotDuration } from '../types/appointmentenums';
import type {
  DoctorSchedule,
  ScheduleConfigPayload,
  SlotConfigResponse,
  SlotGenerationRequest,
  SlotGenerationResponse,
  AvailableSlot,
  ScheduleFilters,
  ScheduleStats,
  ScheduleValidation,
  BulkScheduleOperation,
  ScheduleTemplate,
  ProviderScheduleSummary
} from '../types/schedule';
import {
  validateAndFormatConsultantId,
  createFormattedSchedulePayload,
  isValidConsultantId,
  generateConsultantUUID
} from '../utils/uuidUtils';

const BASE_URL = "https://megha-dev.sirobilt.com";
const USE_MOCK_DATA = false; // Set to false to use real API

// Mock data removed - using real API endpoints

// All mock data removed - using real API endpoints only

// Create Doctor Schedule Configuration
export const createScheduleConfig = async (payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Creating schedule configuration:", payload);

    // Validate and format the consultant ID
    const validatedConsultantId = validateAndFormatConsultantId(payload.consultantId);

    // Create properly formatted payload
    const formattedPayload = createFormattedSchedulePayload({
      ...payload,
      consultantId: validatedConsultantId
    });

    console.log("Formatted payload with validated UUID:", formattedPayload);

    // Use new slot-configs endpoint
    const response = await axios.post(`${BASE_URL}/api/consultants/${validatedConsultantId}/slot-configs`, formattedPayload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to create schedule configuration:", error);

    // Check if it's a UUID validation error
    if (error.message && error.message.includes('Invalid consultant ID format')) {
      return {
        success: false,
        error: `Invalid consultant ID format. Expected UUID format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX`
      };
    }

    return {
      success: false,
      error: error.response?.data?.message || "Failed to create schedule configuration"
    };
  }
};

// Get All Schedule Configurations
export const getScheduleConfigs = async (filters: ScheduleFilters = {}): Promise<{
  results: DoctorSchedule[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}> => {
  try {
    console.log("Fetching schedule configurations with filters:", filters);



    // Use new slot-configs endpoint
    let endpoint = `${BASE_URL}/api/consultants/{consultantId}/slot-configs`;

    // // If consultantId is provided, use specific consultant endpoint
    // if (filters.consultantId) {
    //   endpoint = `${BASE_URL}/api/consultants/${filters.consultantId}/slot-configs`;
    // }

    const response = await axios.get(endpoint);

    // Transform response to match expected format
    const schedules = Array.isArray(response.data) ? response.data : [];

    return {
      results: schedules,
      totalElements: schedules.length,
      totalPages: 1,
      page: 0,
      size: schedules.length
    };
  } catch (error: any) {
    console.error("Failed to fetch schedule configurations:", error);
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20
    };
  }
};

// Update Schedule Configuration
export const updateScheduleConfig = async (consultantId: string, payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Updating schedule configuration for consultant:", consultantId, payload);

    // Validate and format the consultant ID
    const validatedConsultantId = validateAndFormatConsultantId(consultantId);

    // Create properly formatted payload
    const formattedPayload = createFormattedSchedulePayload({
      ...payload,
      consultantId: validatedConsultantId
    });

    console.log("Formatted payload with validated UUID:", formattedPayload);

    // Use new slot-configs endpoint with PUT method
    const response = await axios.put(`${BASE_URL}/api/consultants/${validatedConsultantId}/slot-configs`, formattedPayload);

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to update schedule configuration:", error);

    // Check if it's a UUID validation error
    if (error.message && error.message.includes('Invalid consultant ID format')) {
      return {
        success: false,
        error: `Invalid consultant ID format. Expected UUID format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX`
      };
    }

    return {
      success: false,
      error: error.response?.data?.message || "Failed to update schedule configuration"
    };
  }
};

// Get Schedule Configurations for Specific Consultant
export const getConsultantSlotConfigs = async (consultantId?: string): Promise<{ success: boolean; data?: SlotConfigResponse[]; error?: string }> => {
  try {
    console.log("Fetching slot configurations for consultant:", consultantId);



    // Use new slot-configs endpoint
    let endpoint = `${BASE_URL}/api/consultants/slot-configs`;

    // If consultantId is provided, validate and use specific consultant endpoint
    if (consultantId) {
      try {
        const validatedConsultantId = validateAndFormatConsultantId(consultantId);
        endpoint = `${BASE_URL}/api/consultants/${validatedConsultantId}/slot-configs`;
        console.log("Using validated consultant ID:", validatedConsultantId);
      } catch (validationError: any) {
        return {
          success: false,
          error: `Invalid consultant ID: ${validationError.message}`
        };
      }
    }

    const response = await axios.get(endpoint);

    return {
      success: true,
      data: Array.isArray(response.data) ? response.data : []
    };
  } catch (error: any) {
    console.error("Failed to fetch consultant slot configurations:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to fetch slot configurations"
    };
  }
};

// Delete Schedule Configuration
export const deleteScheduleConfig = async (scheduleId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("Deleting schedule configuration:", scheduleId);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true
      };
    }

    await axios.delete(`${BASE_URL}/api/schedules/${scheduleId}`);
    
    return {
      success: true
    };
  } catch (error: any) {
    console.error("Failed to delete schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to delete schedule configuration"
    };
  }
};

// Generate Slots
export const generateSlots = async (request: SlotGenerationRequest): Promise<SlotGenerationResponse> => {
  try {
    console.log("Generating slots:", request);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true,
        message: "Slots generated successfully",
        data: {
          totalSlotsGenerated: 150,
          dateRange: {
            from: request.from,
            to: request.to
          },
          providersAffected: request.providerId ? [request.providerId] : ["08756dae-86dc-4eaf-b1bb-c0d3faba7e39", "prov-002"],
          conflicts: []
        }
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/generate-slots`, request);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to generate slots:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to generate slots",
      data: {
        totalSlotsGenerated: 0,
        dateRange: { from: request.from, to: request.to },
        providersAffected: [],
        conflicts: []
      }
    };
  }
};

// Get Available Slots for Provider and Date Range
export const getAvailableSlots = async (providerId: string, fromDate: string, toDate: string): Promise<AvailableSlot[]> => {
  try {
    console.log("Fetching available slots:", { providerId, fromDate, toDate });



    const params = new URLSearchParams();
    params.append('providerId', providerId);
    params.append('fromDate', fromDate);
    params.append('toDate', toDate);

    const response = await axios.get(`${BASE_URL}/api/schedules/available-slots?${params}`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch available slots:", error);
    return [];
  }
};

// Get Schedule Statistics
export const getScheduleStats = async (): Promise<ScheduleStats> => {
  try {
    console.log("Fetching schedule statistics");



    const response = await axios.get(`${BASE_URL}/api/schedules/stats`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule statistics:", error);
    return {
      totalSchedules: 0,
      activeSchedules: 0,
      providersWithSchedules: 0,
      totalSlotsGenerated: 0,
      availableSlots: 0,
      bookedSlots: 0,
      upcomingScheduleChanges: 0
    };
  }
};

// Validate Schedule Configuration
export const validateScheduleConfig = async (payload: ScheduleConfigPayload): Promise<ScheduleValidation> => {
  try {
    console.log("Validating schedule configuration:", payload);

    if (USE_MOCK_DATA) {
      // Mock validation
      const errors: string[] = [];
      const warnings: string[] = [];

      // Basic validation
      if (payload.startTime >= payload.endTime) {
        errors.push("End time must be after start time");
      }

      if (payload.daysOfWeek.length === 0) {
        errors.push("At least one day of week must be selected");
      }

      if (new Date(payload.effectiveFrom) >= new Date(payload.effectiveTo)) {
        errors.push("Effective to date must be after effective from date");
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        conflicts: []
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/validate`, payload);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to validate schedule configuration:", error);
    return {
      isValid: false,
      errors: ["Validation failed"],
      warnings: [],
      conflicts: []
    };
  }
};
