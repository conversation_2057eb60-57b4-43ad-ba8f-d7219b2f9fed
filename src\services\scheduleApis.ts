import axios from "axios";
import { DayOfWeek, SlotDuration } from '../types/appointmentenums';
import type {
  DoctorSchedule,
  ScheduleConfigPayload,
  SlotGenerationRequest,
  SlotGenerationResponse,
  AvailableSlot,
  ScheduleFilters,
  ScheduleStats,
  ScheduleValidation,
  BulkScheduleOperation,
  ScheduleTemplate,
  ProviderScheduleSummary
} from '../types/schedule';

const BASE_URL = "https://megha-dev.sirobilt.com";
const USE_MOCK_DATA = true; // Set to false to use real API

// Mock data for development
const mockSchedules: DoctorSchedule[] = [
  {
    scheduleId: "sched-001",
    consultantId: "08756dae-86dc-4eaf-b1bb-c0d3faba7e39",
    daysOfWeek: [DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday],
    startTime: "09:00:00",
    endTime: "17:00:00",
    slotDuration: SlotDuration.Thirty,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  },
  {
    scheduleId: "sched-002",
    consultantId: "prov-002",
    daysOfWeek: [DayOfWeek.Monday, DayOfWeek.Wednesday, DayOfWeek.Friday],
    startTime: "10:00:00",
    endTime: "16:00:00",
    slotDuration: SlotDuration.FortyFive,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  },
  {
    scheduleId: "sched-003",
    consultantId: "prov-003",
    daysOfWeek: [DayOfWeek.Tuesday, DayOfWeek.Thursday, DayOfWeek.Saturday],
    startTime: "08:00:00",
    endTime: "14:00:00",
    slotDuration: SlotDuration.Fifteen, // Using 15 instead of 20 as it's not in enum
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  },
  {
    scheduleId: "sched-004",
    consultantId: "prov-004",
    daysOfWeek: [DayOfWeek.Monday, DayOfWeek.Tuesday, DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday, DayOfWeek.Saturday],
    startTime: "07:00:00",
    endTime: "15:00:00",
    slotDuration: SlotDuration.Fifteen,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  },
  {
    scheduleId: "sched-005",
    consultantId: "prov-005",
    daysOfWeek: [DayOfWeek.Wednesday, DayOfWeek.Thursday, DayOfWeek.Friday],
    startTime: "14:00:00",
    endTime: "20:00:00",
    slotDuration: SlotDuration.Sixty,
    effectiveFrom: "2025-06-18",
    effectiveTo: "2026-06-18",
    isActive: true,
    createdAt: "2025-06-18T10:00:00Z",
    createdBy: "admin-001"
  }
];

// Generate comprehensive mock slots for multiple providers and dates
const generateMockSlots = (): AvailableSlot[] => {
  const slots: AvailableSlot[] = [];
  const providers = ["08756dae-86dc-4eaf-b1bb-c0d3faba7e39", "prov-002", "prov-003", "prov-004", "prov-005"];
  const today = new Date();

  // Generate slots for next 30 days
  for (let dayOffset = 0; dayOffset < 30; dayOffset++) {
    const currentDate = new Date(today);
    currentDate.setDate(today.getDate() + dayOffset);
    const dateString = currentDate.toISOString().split('T')[0];
    const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    providers.forEach(providerId => {
      const schedule = mockSchedules.find(s => s.consultantId === providerId);
      if (!schedule) return;

      // Check if provider works on this day
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const currentDayName = dayNames[dayOfWeek] as DayOfWeek;

      if (!schedule.daysOfWeek.includes(currentDayName)) return;

      // Generate slots for this provider on this day
      const startHour = parseInt(schedule.startTime.split(':')[0]);
      const startMinute = parseInt(schedule.startTime.split(':')[1]);
      const endHour = parseInt(schedule.endTime.split(':')[0]);
      const endMinute = parseInt(schedule.endTime.split(':')[1]);

      const startTimeMinutes = startHour * 60 + startMinute;
      const endTimeMinutes = endHour * 60 + endMinute;
      const slotDuration = schedule.slotDuration as number;

      // Add lunch break (12:00-13:00) for full-day schedules
      const lunchStart = 12 * 60; // 12:00 in minutes
      const lunchEnd = 13 * 60;   // 13:00 in minutes

      for (let currentMinutes = startTimeMinutes; currentMinutes < endTimeMinutes; currentMinutes += slotDuration) {
        const slotEndMinutes = currentMinutes + slotDuration;

        // Skip lunch break
        if (currentMinutes >= lunchStart && currentMinutes < lunchEnd) continue;
        if (slotEndMinutes > lunchStart && slotEndMinutes <= lunchEnd) continue;

        const startHours = Math.floor(currentMinutes / 60);
        const startMins = currentMinutes % 60;
        const endHours = Math.floor(slotEndMinutes / 60);
        const endMins = slotEndMinutes % 60;

        const startTime = `${startHours.toString().padStart(2, '0')}:${startMins.toString().padStart(2, '0')}:00`;
        const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}:00`;

        // Determine slot availability (simulate realistic booking patterns)
        let isBooked = false;
        let isBlocked = false;
        let blockReason = '';

        // Higher booking rate for popular times (10-12, 14-16)
        const hour = Math.floor(currentMinutes / 60);
        let bookingProbability = 0.2; // Base 20% booking rate

        if ((hour >= 10 && hour < 12) || (hour >= 14 && hour < 16)) {
          bookingProbability = 0.4; // 40% for popular times
        }

        // Past dates should have higher booking rates
        if (dayOffset < 7) {
          bookingProbability += 0.3;
        }

        // Random booking simulation
        if (Math.random() < bookingProbability) {
          isBooked = true;
        }

        // Add some blocked slots (breaks, meetings, etc.)
        if (!isBooked && Math.random() < 0.05) { // 5% blocked
          isBlocked = true;
          const reasons = ['Staff Meeting', 'Equipment Maintenance', 'Emergency', 'Training Session'];
          blockReason = reasons[Math.floor(Math.random() * reasons.length)];
        }

        // Lunch break slots
        if (currentMinutes >= lunchStart && currentMinutes < lunchEnd) {
          isBlocked = true;
          blockReason = 'Lunch Break';
        }

        const slot: AvailableSlot = {
          slotId: `slot-${providerId}-${dateString}-${startTime.replace(/:/g, '')}`,
          providerId,
          date: dateString,
          startTime,
          endTime,
          duration: slotDuration,
          isAvailable: !isBooked && !isBlocked,
          isBooked,
          isBlocked,
          blockReason: blockReason || undefined,
          appointmentId: isBooked ? `apt-${Math.random().toString(36).substr(2, 9)}` : undefined
        };

        slots.push(slot);
      }
    });
  }

  return slots;
};

const mockAvailableSlots = generateMockSlots();

// Create Doctor Schedule Configuration
export const createScheduleConfig = async (payload: ScheduleConfigPayload): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Creating schedule configuration:", payload);

    if (USE_MOCK_DATA) {
      // Mock implementation
      const newSchedule: DoctorSchedule = {
        scheduleId: `sched-${Date.now()}`,
        consultantId: payload.consultantId,
        daysOfWeek: payload.daysOfWeek as any[],
        startTime: payload.startTime,
        endTime: payload.endTime,
        slotDuration: payload.slotDuration as any,
        effectiveFrom: payload.effectiveFrom,
        effectiveTo: payload.effectiveTo,
        isActive: true,
        createdAt: new Date().toISOString(),
        createdBy: "current-user"
      };

      return {
        success: true,
        data: newSchedule
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules`, payload);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to create schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to create schedule configuration"
    };
  }
};

// Get All Schedule Configurations
export const getScheduleConfigs = async (filters: ScheduleFilters = {}): Promise<{
  results: DoctorSchedule[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
}> => {
  try {
    console.log("Fetching schedule configurations with filters:", filters);

    if (USE_MOCK_DATA) {
      // Mock implementation with filtering
      let filteredSchedules = [...mockSchedules];
      
      if (filters.consultantId) {
        filteredSchedules = filteredSchedules.filter(s => s.consultantId === filters.consultantId);
      }
      
      if (filters.isActive !== undefined) {
        filteredSchedules = filteredSchedules.filter(s => s.isActive === filters.isActive);
      }

      return {
        results: filteredSchedules,
        totalElements: filteredSchedules.length,
        totalPages: 1,
        page: 0,
        size: filteredSchedules.length
      };
    }

    const params = new URLSearchParams();
    if (filters.consultantId) params.append('consultantId', filters.consultantId);
    if (filters.dayOfWeek) params.append('dayOfWeek', filters.dayOfWeek);
    if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters.effectiveFrom) params.append('effectiveFrom', filters.effectiveFrom);
    if (filters.effectiveTo) params.append('effectiveTo', filters.effectiveTo);
    params.append('page', (filters.page || 0).toString());
    params.append('size', (filters.size || 20).toString());

    const response = await axios.get(`${BASE_URL}/api/schedules?${params}`);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule configurations:", error);
    return {
      results: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20
    };
  }
};

// Update Schedule Configuration
export const updateScheduleConfig = async (scheduleId: string, payload: Partial<ScheduleConfigPayload>): Promise<{ success: boolean; data?: DoctorSchedule; error?: string }> => {
  try {
    console.log("Updating schedule configuration:", scheduleId, payload);

    if (USE_MOCK_DATA) {
      // Mock implementation
      const existingSchedule = mockSchedules.find(s => s.scheduleId === scheduleId);
      if (!existingSchedule) {
        return {
          success: false,
          error: "Schedule not found"
        };
      }

      const updatedSchedule: DoctorSchedule = {
        ...existingSchedule,
        ...payload,
        daysOfWeek: payload.daysOfWeek ? payload.daysOfWeek.map(day => day as DayOfWeek) : existingSchedule.daysOfWeek,
        updatedAt: new Date().toISOString(),
        updatedBy: "current-user"
      };

      return {
        success: true,
        data: updatedSchedule
      };
    }

    const response = await axios.put(`${BASE_URL}/api/schedules/${scheduleId}`, payload);
    
    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    console.error("Failed to update schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to update schedule configuration"
    };
  }
};

// Delete Schedule Configuration
export const deleteScheduleConfig = async (scheduleId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("Deleting schedule configuration:", scheduleId);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true
      };
    }

    await axios.delete(`${BASE_URL}/api/schedules/${scheduleId}`);
    
    return {
      success: true
    };
  } catch (error: any) {
    console.error("Failed to delete schedule configuration:", error);
    return {
      success: false,
      error: error.response?.data?.message || "Failed to delete schedule configuration"
    };
  }
};

// Generate Slots
export const generateSlots = async (request: SlotGenerationRequest): Promise<SlotGenerationResponse> => {
  try {
    console.log("Generating slots:", request);

    if (USE_MOCK_DATA) {
      // Mock implementation
      return {
        success: true,
        message: "Slots generated successfully",
        data: {
          totalSlotsGenerated: 150,
          dateRange: {
            from: request.from,
            to: request.to
          },
          providersAffected: request.providerId ? [request.providerId] : ["08756dae-86dc-4eaf-b1bb-c0d3faba7e39", "prov-002"],
          conflicts: []
        }
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/generate-slots`, request);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to generate slots:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Failed to generate slots",
      data: {
        totalSlotsGenerated: 0,
        dateRange: { from: request.from, to: request.to },
        providersAffected: [],
        conflicts: []
      }
    };
  }
};

// Get Available Slots for Provider and Date Range
export const getAvailableSlots = async (providerId: string, fromDate: string, toDate: string): Promise<AvailableSlot[]> => {
  try {
    console.log("Fetching available slots:", { providerId, fromDate, toDate });

    if (USE_MOCK_DATA) {
      // Mock implementation - filter by provider and date range
      const filteredSlots = mockAvailableSlots.filter(slot =>
        slot.providerId === providerId &&
        slot.date >= fromDate &&
        slot.date <= toDate
      );

      console.log(`Found ${filteredSlots.length} slots for provider ${providerId} between ${fromDate} and ${toDate}`);

      // Sort by date and time
      return filteredSlots.sort((a, b) => {
        if (a.date !== b.date) {
          return a.date.localeCompare(b.date);
        }
        return a.startTime.localeCompare(b.startTime);
      });
    }

    const params = new URLSearchParams();
    params.append('providerId', providerId);
    params.append('fromDate', fromDate);
    params.append('toDate', toDate);

    const response = await axios.get(`${BASE_URL}/api/schedules/available-slots?${params}`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch available slots:", error);
    return [];
  }
};

// Get Schedule Statistics
export const getScheduleStats = async (): Promise<ScheduleStats> => {
  try {
    console.log("Fetching schedule statistics");

    if (USE_MOCK_DATA) {
      // Calculate real statistics from mock data
      const totalSchedules = mockSchedules.length;
      const activeSchedules = mockSchedules.filter(s => s.isActive).length;
      const providersWithSchedules = new Set(mockSchedules.map(s => s.consultantId)).size;
      const totalSlotsGenerated = mockAvailableSlots.length;
      const availableSlots = mockAvailableSlots.filter(s => s.isAvailable).length;
      const bookedSlots = mockAvailableSlots.filter(s => s.isBooked).length;

      return {
        totalSchedules,
        activeSchedules,
        providersWithSchedules,
        totalSlotsGenerated,
        availableSlots,
        bookedSlots,
        upcomingScheduleChanges: 2
      };
    }

    const response = await axios.get(`${BASE_URL}/api/schedules/stats`);

    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch schedule statistics:", error);
    return {
      totalSchedules: 0,
      activeSchedules: 0,
      providersWithSchedules: 0,
      totalSlotsGenerated: 0,
      availableSlots: 0,
      bookedSlots: 0,
      upcomingScheduleChanges: 0
    };
  }
};

// Validate Schedule Configuration
export const validateScheduleConfig = async (payload: ScheduleConfigPayload): Promise<ScheduleValidation> => {
  try {
    console.log("Validating schedule configuration:", payload);

    if (USE_MOCK_DATA) {
      // Mock validation
      const errors: string[] = [];
      const warnings: string[] = [];

      // Basic validation
      if (payload.startTime >= payload.endTime) {
        errors.push("End time must be after start time");
      }

      if (payload.daysOfWeek.length === 0) {
        errors.push("At least one day of week must be selected");
      }

      if (new Date(payload.effectiveFrom) >= new Date(payload.effectiveTo)) {
        errors.push("Effective to date must be after effective from date");
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        conflicts: []
      };
    }

    const response = await axios.post(`${BASE_URL}/api/schedules/validate`, payload);
    
    return response.data;
  } catch (error: any) {
    console.error("Failed to validate schedule configuration:", error);
    return {
      isValid: false,
      errors: ["Validation failed"],
      warnings: [],
      conflicts: []
    };
  }
};
