import React, { useEffect, useState } from "react";
import {
  <PERSON>a<PERSON><PERSON>ch, FaUserMd, FaTrash, <PERSON>a<PERSON>ye, FaEdit
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { getAllDoctors, deleteDoctor } from "../../services/doctorApis";
import DoctorProfileModal from "./DoctorProfileModal";
import ConfirmDialog from "../../utils/ConfirmDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Doctor {
  doctorId: string;
  fullName: string;
  gender: string;
  dateOfBirth: string;
  mobileNumber: string;
  email: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  createdAt: string;
}

const DoctorListPage = () => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string | null>(null);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const docRes = await getAllDoctors();
        if (!docRes.success) throw new Error("Failed to load doctors");

        const transformedDoctors: Doctor[] = docRes.data.map((doc: any): Doctor => ({
          doctorId: doc.doctorId,
          fullName: doc.fullName,
          gender: doc.gender,
          dateOfBirth: doc.dateOfBirth,
          mobileNumber: doc.mobileNumber,
          email: doc.email,
          registrationNumber: doc.registrationNumber,
          registrationState: doc.registrationState,
          yearsOfExperience: doc.yearsOfExperience,
          telemedicineReady: doc.telemedicineReady,
          languagesSpoken: Array.isArray(doc.languagesSpoken)
            ? doc.languagesSpoken.join(", ")
            : doc.languagesSpoken,
          isActive: doc.isActive,
          address: doc.address || {
            street: "",
            city: "",
            state: "",
            zipCode: "",
            country: ""
          },
          createdAt: doc.createdAt,
        }));

        setDoctors(transformedDoctors);
      } catch (err) {
        console.error(err);
        toast.error("Unexpected error occurred.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredDoctors = doctors.filter(doc =>
    doc.fullName.toLowerCase().includes(search.toLowerCase()) ||
    doc.registrationNumber.toLowerCase().includes(search.toLowerCase())
  );

  const handleDelete = async () => {
    if (!confirmDeleteId) return;
    const res = await deleteDoctor(confirmDeleteId);
    if (res.success) {
      setDoctors(prev => prev.filter(d => d.doctorId !== confirmDeleteId));
      toast.success("Doctor deleted.");
    } else {
      toast.error("Delete failed.");
    }
    setConfirmDeleteId(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 font-sans">
      <ToastContainer />
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-pink-500">Doctor Management</h1>
            <p className="text-gray-600 text-lg">Manage hospital doctors and their schedules</p>
          </div>
          <button
            onClick={() => navigate("/doctors/add")}
            className="bg-gradient-to-r from-indigo-600 to-pink-500 text-white px-6 py-3 rounded-xl hover:scale-105 transition-all shadow-lg"
          >
            <FaUserMd className="inline mr-2" /> Add Doctor
          </button>
        </div>

        <div className="bg-white rounded-xl shadow p-4 mb-6">
          <div className="relative">
            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              className="w-full pl-12 pr-4 py-3 text-base border border-gray-300 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-200"
              placeholder="Search by name or license number..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
        </div>

        {loading ? (
          <div className="text-center py-20 text-gray-500">Loading doctors...</div>
        ) : (
          <div className="overflow-hidden rounded-2xl shadow-lg border border-gray-200 bg-white">
            <table className="w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {["Name", "Gender", "Phone", "Email", "Experience", "Address", "Actions"].map(h => (
                    <th key={h} className="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wide">{h}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredDoctors.length === 0 ? (
                  <tr><td colSpan={7} className="text-center py-12 text-gray-500">No doctors found.</td></tr>
                ) : (
                  filteredDoctors.map((doc) => (
                    <tr key={doc.doctorId} className="hover:bg-blue-50 transition">
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-md">
                            {doc.fullName
                              .split(" ")
                              .map(n => n[0])
                              .slice(0, 2)
                              .join("")
                              .toUpperCase()}
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{doc.fullName}</div>
                            <div className="text-xs text-gray-500 truncate max-w-[180px]">
                              dr.{doc.fullName.replace(/ /g, "").toLowerCase()}@hospital.com
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.gender}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.mobileNumber}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.email}</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.yearsOfExperience} yrs</td>
                      <td className="px-4 py-4 text-sm text-gray-700">{doc.address.city}, {doc.address.state}</td>
                      <td className="px-4 py-4">
                        <div className="flex gap-2">
                          <button
                            className="bg-green-100 text-green-600 p-2 rounded-lg hover:bg-green-200"
                            title="View Profile"
                            onClick={() => setSelectedDoctorId(doc.doctorId)}
                          >
                            <FaEye />
                          </button>
                          <button
                            className="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200"
                            title="Edit"
                            onClick={() => navigate(`/doctors/edit/${doc.doctorId}`)}
                          >
                            <FaEdit />
                          </button>
                          <button
                            className="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200"
                            title="Delete"
                            onClick={() => setConfirmDeleteId(doc.doctorId)}
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {selectedDoctorId && (
          <DoctorProfileModal
            doctorId={selectedDoctorId}
            onClose={() => setSelectedDoctorId(null)}
          />
        )}

        <ConfirmDialog
          isOpen={!!confirmDeleteId}
          title="Delete Doctor"
          message="Are you sure you want to delete this doctor? This action cannot be undone."
          onConfirm={handleDelete}
          onCancel={() => setConfirmDeleteId(null)}
        />
      </div>
    </div>
  );
};

export default DoctorListPage;
