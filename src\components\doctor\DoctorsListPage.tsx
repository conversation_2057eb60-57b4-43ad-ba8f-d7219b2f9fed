import React, { useEffect, useState, useMemo, useRef } from "react";
import {
  FaS<PERSON>ch,
  FaEllipsisV,
  FaUserMd,
  FaReg<PERSON><PERSON>,
  FaTools,
  FaTrash,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { getAllDoctors, deleteDoctor } from "../../services/doctorApis";
import { getDepartments } from "../../services/departmentApis";
import DoctorProfileModal from "./DoctorProfileModal";
import ConfirmDialog from "../../utils/ConfirmDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface OperatingHour {
  dayOfWeek: string;
  isOperating: boolean;
  startTime: string;
  endTime: string;
}

interface Department {
  id: string;
  name: string;
  description: string;
  operatingHours?: OperatingHour[];
}

interface Specialization {
  name: string;
  description: string;
  operatingHours: OperatingHour[];
}

interface Doctor {
  doctorId: string;
  fullName: string;
  registrationNumber: string;
  registrationState: string;
  yearsOfExperience: number;
  telemedicineReady: boolean;
  languagesSpoken: string;
  isActive: boolean;
  specialization: Specialization;
}

const DoctorListPage = () => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string | null>(null);
  const [doctorToDelete, setDoctorToDelete] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const fetchDoctors = async () => {
    try {
      setLoading(true);
      const [doctorRes, deptRes] = await Promise.all([
        getAllDoctors(),
        getDepartments(),
      ]);

      if (doctorRes.success && deptRes.success) {
        const deptMap = new Map<string, Department>(
          (deptRes.data.results || []).map((d: Department) => [d.id, d])
        );

        const transformed: Doctor[] = doctorRes.data.map((dto: any) => {
          const dept = deptMap.get(dto.specialization);
          const specialization: Specialization = dept
            ? {
                name: dept.name,
                description: dept.description,
                operatingHours: dept.operatingHours || [],
              }
            : {
                name: "N/A",
                description: "N/A",
                operatingHours: [],
              };

          return {
            doctorId: dto.doctorId || "unknown",
            fullName: dto.fullName,
            registrationNumber: dto.registrationNumber,
            registrationState: dto.registrationState,
            yearsOfExperience: dto.yearsOfExperience,
            telemedicineReady: dto.telemedicineReady,
            languagesSpoken: dto.languagesSpoken,
            isActive: dto.isActive,
            specialization,
          };
        });

        setDoctors(transformed);
      } else {
        toast.error("Failed to load doctors or departments.");
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDoctors();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdownIndex(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDeleteDoctor = async () => {
    if (doctorToDelete) {
      const response = await deleteDoctor(doctorToDelete);
      if (response.success) {
        setDoctors(prev => prev.filter(doc => doc.doctorId !== doctorToDelete));
        toast.success("Doctor deleted successfully.");
      } else {
        console.error("Failed to delete doctor:", response.error);
        toast.error("Failed to delete doctor.");
      }
      setShowConfirmDialog(false);
      setDoctorToDelete(null);
    }
  };

  const filteredDoctors = useMemo(() => {
    const q = searchQuery.trim().toLowerCase();
    if (!q) return doctors;
    return doctors.filter((doc) =>
      doc.fullName.toLowerCase().includes(q) ||
      doc.registrationNumber.toLowerCase().includes(q)
    );
  }, [searchQuery, doctors]);

  const renderAvailability = (doc: Doctor) => {
    const hours = doc.specialization.operatingHours;
    if (!hours?.length) {
      return (
        <span className="inline-flex items-center gap-1 text-sm text-gray-400 italic">
          <FaRegClock />
          Not Available
        </span>
      );
    }

    return (
      <div className="flex flex-wrap gap-2">
        {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day, idx) => {
          const slot = hours.find(h => h.dayOfWeek.toLowerCase() === day.toLowerCase());
          const shortDay = day.slice(0, 3);
          return (
            <span
              key={idx}
              className={`inline-flex items-center gap-1 px-2 py-1 text-xs rounded-full border ${
                slot?.isOperating
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-gray-100 text-gray-400 border-gray-200"
              }`}
            >
              <FaRegClock className="text-xs" />
              {shortDay}: {slot?.isOperating ? `${slot.startTime}–${slot.endTime}` : "Off"}
            </span>
          );
        })}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <ToastContainer position="top-right" autoClose={3000} />

        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Doctor Management
            </h1>
            <p className="text-gray-600 text-lg">View and manage hospital doctors and schedules</p>
          </div>
          <button
            onClick={() => navigate("/doctors/add")}
            className="group relative inline-flex items-center justify-center px-6 py-3 text-white text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 hover:scale-105 transition-all"
          >
            <FaUserMd className="mr-2" />
            Add Doctor
            <div className="absolute inset-0 bg-white opacity-20 rounded-xl blur-lg group-hover:opacity-30" />
          </button>
        </div>

        <div className="bg-white rounded-2xl shadow-md p-6 mb-6">
          <div className="relative">
            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              className="block w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500 hover:bg-white"
              placeholder="Search by name or license number..."
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {loading ? (
          <div className="text-center py-20 text-gray-500">Loading doctors...</div>
        ) : (
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
            <table className="w-full table-auto divide-y divide-gray-200">
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                <tr>
                  {["Name", "Department", "Specialization", "Experience", "License", "Availability", ""].map((h) => (
                    <th
                      key={h}
                      className="px-4 py-3 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider"
                    >
                      {h}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredDoctors.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                      No doctors found.
                    </td>
                  </tr>
                ) : (
                  filteredDoctors.map((doc, index) => (
                    <tr key={doc.doctorId} className="hover:bg-gray-50 transition">
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                            {doc.fullName[0]}
                          </div>
                          <div>
                            <div>{doc.fullName}</div>
                            <div className="text-xs text-gray-500">
                              dr.{doc.fullName.toLowerCase().replace(/ /g, "")}@hospital.com
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-600">{doc.specialization.name}</td>
                      <td className="px-4 py-4 text-sm text-gray-600">{doc.specialization.description}</td>
                      <td className="px-4 py-4 text-sm text-gray-600">{doc.yearsOfExperience} years</td>
                      <td className="px-4 py-4 text-sm text-gray-600">{doc.registrationNumber}</td>
                      <td className="px-4 py-4 text-sm text-gray-600">{renderAvailability(doc)}</td>
                      <td className="px-4 py-4 text-right relative">
                        <button
                          onClick={() => setOpenDropdownIndex(openDropdownIndex === index ? null : index)}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <FaEllipsisV />
                        </button>
                        {openDropdownIndex === index && (
                          <div
                            ref={dropdownRef}
                            className="absolute bottom-full right-0 mb-3 w-56 bg-white border border-gray-200 rounded-xl shadow-xl z-50"
                          >
                            <button
                              className="block w-full text-left px-4 py-3 text-sm hover:bg-gray-50"
                              onClick={() => {
                                setSelectedDoctorId(doc.doctorId);
                                setOpenDropdownIndex(null);
                              }}
                            >
                              👁️ View Profile
                            </button>
                            <button
                              className="block w-full text-left px-4 py-3 text-sm hover:bg-gray-50"
                              onClick={() => navigate(`/doctors/edit/${doc.doctorId}`)}
                            >
                              ✏️ Edit Details
                            </button>
                            <button
                              className="block w-full text-left px-4 py-3 text-sm hover:bg-gray-50"
                              onClick={() => navigate(`/doctors/schedule/${doc.doctorId}`)}
                            >
                              📅 View Schedule
                            </button>
                            <button
                              className="flex items-center gap-2 w-full text-left px-4 py-3 text-sm hover:bg-gray-50"
                              onClick={() => navigate(`/doctors/availability/${doc.doctorId}`)}
                            >
                              <FaTools className="text-gray-500" />
                              Manage Availability
                            </button>
                            <button
                              className="flex items-center gap-2 w-full text-left px-4 py-3 text-sm hover:bg-gray-50 text-red-600"
                              onClick={() => {
                                setDoctorToDelete(doc.doctorId);
                                setShowConfirmDialog(true);
                                setOpenDropdownIndex(null);
                              }}
                            >
                              <FaTrash className="text-red-500" />
                              Delete Doctor
                            </button>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {selectedDoctorId && (
          <DoctorProfileModal
            doctorId={selectedDoctorId}
            onClose={() => setSelectedDoctorId(null)}
          />
        )}

        <ConfirmDialog
          isOpen={showConfirmDialog}
          title="Delete Doctor"
          message="Are you sure you want to delete this doctor? This action cannot be undone."
          onConfirm={handleDeleteDoctor}
          onCancel={() => setShowConfirmDialog(false)}
        />
      </div>
    </div>
  );
};

export default DoctorListPage;
