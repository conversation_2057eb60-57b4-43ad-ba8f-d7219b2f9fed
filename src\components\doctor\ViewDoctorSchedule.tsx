// pages/doctor/ViewDoctorSchedule.tsx
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { getDoctorById } from "../../services/doctorApis";
import { getDepartmentById } from "../../services/departmentApis";

const ViewDoctorSchedule = () => {
  const { id } = useParams();
  const [schedule, setSchedule] = useState([]);

  useEffect(() => {
    const fetchSchedule = async () => {
      const res = await getDoctorById(id!);
      if (res.success) {
        const dept = await getDepartmentById(res.data.specialization);
        if (dept.success) {
          setSchedule(dept.data.operatingHours || []);
        }
      }
    };
    fetchSchedule();
  }, [id]);

  return (
    <div className="p-6 max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Doctor Schedule</h2>
      <ul className="space-y-2">
        {schedule.length === 0 ? (
          <p>No schedule available.</p>
        ) : (
          schedule.map((entry: any, idx) => (
            <li key={idx} className="border p-3 rounded bg-gray-50">
              <strong>{entry.dayOfWeek}</strong>:{" "}
              {entry.isOperating
                ? `${entry.startTime} - ${entry.endTime}`
                : "Not available"}
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default ViewDoctorSchedule;
