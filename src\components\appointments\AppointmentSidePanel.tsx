import React, { useState, useEffect } from 'react';
import { X, User, Calendar, Clock, MapPin, FileText, AlertCircle } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { AppointmentForm } from './AppointmentForm';
import { getProviders } from '../../services/providerApis';
import { getAppointmentsByProvider } from '../../services/appointmentApis';
import { showSuccess, showError } from '../../utils/toastUtils';
import type { Provider } from '../../types/provider';
import type { Appointment } from '../../types/appointment';

interface AppointmentSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: string;
  selectedTime?: string;
  onAppointmentCreated: (appointment: Appointment) => void;
  editingAppointment?: Appointment | null;
}

const AppointmentSidePanel: React.FC<AppointmentSidePanelProps> = ({
  isOpen,
  onClose,
  selectedDate,
  selectedTime,
  onAppointmentCreated,
  editingAppointment
}) => {
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerAppointments, setProviderAppointments] = useState<Appointment[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [bookedSlots, setBookedSlots] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadProviders();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedProvider) {
      loadProviderAppointments();
    }
  }, [selectedProvider, selectedDate]);

  const loadProviders = async () => {
    try {
      const response = await getProviders({ isActive: true, size: 100 });
      setProviders(response.results || []);
    } catch (error) {
      console.error('Failed to load providers:', error);
      showError('Failed to load providers');
    }
  };

  const loadProviderAppointments = async () => {
    if (!selectedProvider) {
      console.log('No provider selected, skipping appointment load');
      return;
    }

    setLoading(true);
    try {
      console.log('🔍 Loading appointments for provider:', {
        providerId: selectedProvider.providerId,
        providerName: `${selectedProvider.firstName} ${selectedProvider.lastName}`,
        selectedDate: selectedDate,
        currentDate: new Date().toISOString().split('T')[0]
      });

      // Get appointments for the selected date or current date if no date selected
      const targetDate = selectedDate || new Date().toISOString().split('T')[0];
      const dateFrom = targetDate;
      const dateTo = targetDate;

      console.log('📅 Fetching appointments with date range:', { dateFrom, dateTo });

      let appointments = await getAppointmentsByProvider(
        selectedProvider.providerId,
        dateFrom,
        dateTo
      );

      console.log('📋 Received appointments from API (with date filter):', {
        count: appointments.length,
        appointments: appointments.map(apt => ({
          id: apt.appointmentId,
          date: apt.appointmentDate,
          time: apt.startTime,
          patient: apt.patient?.firstName + ' ' + apt.patient?.lastName,
          status: apt.status
        }))
      });

      // If no appointments found with date filter, try without date filter for debugging
      if (appointments.length === 0) {
        console.log('🔍 No appointments found with date filter, trying without date filter...');
        appointments = await getAppointmentsByProvider(selectedProvider.providerId);

        console.log('📋 Received appointments from API (without date filter):', {
          count: appointments.length,
          appointments: appointments.map(apt => ({
            id: apt.appointmentId,
            date: apt.appointmentDate,
            time: apt.startTime,
            patient: apt.patient?.firstName + ' ' + apt.patient?.lastName,
            status: apt.status
          }))
        });
      }

      setProviderAppointments(appointments);

      // Extract booked slots for the target date
      const dateAppointments = appointments.filter(apt => {
        // Handle different date formats from API
        let aptDate = apt.appointmentDate;
        if (aptDate.includes('T')) {
          aptDate = aptDate.split('T')[0]; // Extract date part
        }

        const matches = aptDate === targetDate && apt.status !== 'Cancelled';
        console.log(`🔍 Checking appointment: ${apt.appointmentId}`, {
          aptDate,
          targetDate,
          status: apt.status,
          matches
        });
        return matches;
      });

      console.log('✅ Filtered appointments for target date:', {
        targetDate,
        count: dateAppointments.length,
        appointments: dateAppointments
      });

      // Extract time slots (handle different time formats)
      const slots = dateAppointments.map(apt => {
        // Handle time format - extract HH:MM from various formats
        let timeSlot = apt.startTime;
        if (apt.startTime.includes('T')) {
          timeSlot = apt.startTime.split('T')[1].substring(0, 5);
        } else if (apt.startTime.includes(':')) {
          timeSlot = apt.startTime.substring(0, 5);
        }
        console.log(`⏰ Extracted time slot: ${apt.startTime} -> ${timeSlot}`);
        return timeSlot;
      });

      setBookedSlots(slots);
      console.log(`🎯 Final result - Found ${slots.length} booked slots for ${targetDate}:`, slots);

    } catch (error) {
      console.error('❌ Failed to load provider appointments:', error);
      setProviderAppointments([]);
      setBookedSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleProviderChange = (providerId: string) => {
    const provider = providers.find(p => p.providerId === providerId);
    setSelectedProvider(provider || null);
  };

  const handleAppointmentSuccess = (appointment: Appointment) => {
    onAppointmentCreated(appointment);
    showSuccess(editingAppointment ? 'Appointment updated successfully' : 'Appointment created successfully');
    onClose();
  };

  const isSlotBooked = (time: string) => {
    return bookedSlots.includes(time);
  };

  const getProviderSchedule = () => {
    if (!selectedProvider) {
      console.log('No provider selected for schedule display');
      return [];
    }

    // Use selected date or current date
    const targetDate = selectedDate || new Date().toISOString().split('T')[0];

    console.log('📅 Getting provider schedule for date:', targetDate);
    console.log('📋 Available appointments:', providerAppointments.length);

    const filteredAppointments = providerAppointments.filter(apt => {
      // Handle different date formats from API
      let aptDate = apt.appointmentDate;
      if (aptDate.includes('T')) {
        aptDate = aptDate.split('T')[0]; // Extract date part
      }

      const matches = aptDate === targetDate;
      console.log(`🔍 Schedule filter: ${apt.appointmentId}`, {
        aptDate,
        targetDate,
        matches
      });
      return matches;
    }).sort((a, b) => {
      // Sort by time, handling different time formats
      const timeA = a.startTime.includes('T') ? a.startTime.split('T')[1] : a.startTime;
      const timeB = b.startTime.includes('T') ? b.startTime.split('T')[1] : b.startTime;
      return timeA.localeCompare(timeB);
    });

    console.log('✅ Filtered schedule appointments:', filteredAppointments.length);
    return filteredAppointments;
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop with blur effect */}
      <div className="fixed inset-0 z-50">
        <div
          className="absolute inset-0"
          style={{
            backdropFilter: 'blur(8px) saturate(180%)',
            WebkitBackdropFilter: 'blur(8px) saturate(180%)',
            backgroundColor: 'rgba(255, 255, 255, 0.25)',
            border: '1px solid rgba(255, 255, 255, 0.18)'
          }}
          onClick={onClose}
        />
      </div>

      {/* Side panel container */}
      <div className="fixed inset-0 z-50 overflow-hidden pointer-events-none">
        <div className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-2xl border-l border-gray-200 pointer-events-auto">
        <div className="flex h-full">
          {/* Main Form Section */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {editingAppointment ? 'Edit Appointment' : 'Create New Appointment'}
                </h2>
                {selectedDate && (
                  <p className="text-sm text-gray-600 mt-1">
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                    {selectedTime && ` at ${selectedTime}`}
                  </p>
                )}
              </div>
              <Button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </Button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <AppointmentForm
                selectedDate={selectedDate}
                selectedTime={selectedTime}
                onSuccess={handleAppointmentSuccess}
                onProviderChange={handleProviderChange}
                bookedSlots={bookedSlots}
                editingAppointment={editingAppointment}
                isSlotBooked={isSlotBooked}
              />
            </div>
          </div>

          {/* Provider Info & Schedule Section */}
          {selectedProvider && (
            <div className="w-80 border-l border-gray-200 bg-gray-50">
              {/* Provider Profile */}
              <div className="p-6 border-b border-gray-200 bg-white">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {selectedProvider.title} {selectedProvider.firstName} {selectedProvider.lastName}
                    </h3>
                    <p className="text-sm text-gray-600">{selectedProvider.specialization}</p>
                    <p className="text-xs text-gray-500">{selectedProvider.department}</p>
                  </div>
                </div>
                
                <div className="mt-4 space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <MapPin size={16} className="text-gray-400" />
                    <span className="text-gray-600">
                      {selectedProvider.department || 'Main Hospital'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileText size={16} className="text-gray-400" />
                    <span className="text-gray-600">
                      License: {selectedProvider.licenseNumber || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Provider Schedule */}
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Calendar size={20} className="text-gray-600" />
                  <h4 className="font-medium text-gray-900">
                    {selectedDate ? `Schedule for ${selectedDate}` : `Today's Schedule`}
                  </h4>
                </div>



                {loading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">Loading schedule...</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {getProviderSchedule().length > 0 ? (
                      getProviderSchedule().map((appointment, index) => (
                        <div
                          key={appointment.appointmentId || index}
                          className={`p-3 rounded-lg border ${
                            appointment.status === 'Cancelled'
                              ? 'bg-red-50 border-red-200'
                              : appointment.status === 'Completed'
                              ? 'bg-green-50 border-green-200'
                              : 'bg-blue-50 border-blue-200'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Clock size={14} className="text-gray-500" />
                              <span className="text-sm font-medium">
                                {appointment.startTime.includes('T')
                                  ? appointment.startTime.split('T')[1].substring(0, 5)
                                  : appointment.startTime.substring(0, 5)
                                } - {appointment.endTime.includes('T')
                                  ? appointment.endTime.split('T')[1].substring(0, 5)
                                  : appointment.endTime.substring(0, 5)
                                }
                              </span>
                            </div>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              appointment.status === 'Cancelled'
                                ? 'bg-red-100 text-red-800'
                                : appointment.status === 'Completed'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {appointment.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {appointment.patient?.firstName} {appointment.patient?.lastName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {appointment.type} - {appointment.reason || 'General consultation'}
                          </p>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                        <p className="text-sm text-gray-500">
                          No appointments scheduled
                          {selectedDate ? ` for ${selectedDate}` : ' for today'}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Booked Slots Warning */}
                {bookedSlots.length > 0 && selectedDate && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertCircle size={16} className="text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">
                        Unavailable Times
                      </span>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {bookedSlots.map((slot, index) => (
                        <span
                          key={index}
                          className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded"
                        >
                          {slot}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      </div>
    </>
  );
};

export default AppointmentSidePanel;
