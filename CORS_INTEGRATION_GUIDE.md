# 🔧 CORS Integration Guide - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Backend

## 🚨 Current Issue

The frontend application is experiencing CORS (Cross-Origin Resource Sharing) errors when trying to access the MeghaSanjeevini backend APIs at `https://megha-dev.sirobilt.com/q/api` from the development server running on `http://localhost:5173`.

### Error Details:
```
Access to fetch at 'https://megha-dev.sirobilt.com/q/api/departments' from origin 'http://localhost:5173' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## ✅ Solutions Implemented

### 1. **Vite Proxy Configuration** (Primary Solution)
- **File**: `vite.config.ts`
- **Purpose**: Proxy API requests through the development server to avoid CORS
- **Configuration**:
```typescript
server: {
  proxy: {
    '/api': {
      target: 'https://megha-dev.sirobilt.com/q',
      changeOrigin: true,
      secure: true,
    }
  }
}
```

### 2. **Dynamic API Base URL**
- **File**: `src/config/apiConfig.ts`
- **Purpose**: Use proxy in development, direct URL in production
- **Configuration**:
```typescript
BASE_URL: import.meta.env.DEV ? '/api' : 'https://megha-dev.sirobilt.com/q/api'
```

### 3. **Mock Data Fallback**
- **File**: `src/config/apiConfig.ts`
- **Purpose**: Temporarily use mock data while CORS issues are resolved
- **Current Setting**: `USE_MOCK_DATA: true` (temporary)

### 4. **CORS Testing Tools**
- **Route**: `/cors-test`
- **Component**: `src/components/test/CorsTestComponent.tsx`
- **Purpose**: Test different connection methods and diagnose CORS issues

## 🔄 How to Fix CORS Issues

### **Option 1: Backend CORS Configuration (Recommended)**
Ask the backend team to add CORS headers to allow requests from `http://localhost:5173`:

```java
// Spring Boot example
@CrossOrigin(origins = {"http://localhost:5173", "https://your-frontend-domain.com"})
@RestController
public class DepartmentController {
    // ... controller methods
}
```

Or configure globally:
```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList("http://localhost:5173"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/**", configuration);
        return source;
    }
}
```

### **Option 2: Use Vite Proxy (Current Implementation)**
The proxy is already configured. To use it:
1. Restart the development server: `npm run dev`
2. Set `USE_MOCK_DATA: false` in `src/config/apiConfig.ts`
3. The app will use `/api` which gets proxied to the backend

### **Option 3: Browser Extension (Development Only)**
Install a CORS browser extension like "CORS Unblock" for Chrome/Firefox.
⚠️ **Warning**: Only use this for development, never in production.

### **Option 4: Mock Data (Current Temporary Solution)**
Keep `USE_MOCK_DATA: true` in `src/config/apiConfig.ts` until CORS is resolved.

## 🧪 Testing the Integration

### **1. CORS Test Page**
- Navigate to `/cors-test`
- Test different connection methods
- View detailed error information

### **2. API Integration Test**
- Navigate to `/api-test`
- Test all CRUD operations
- Monitor network tab for errors

### **3. Manual Testing**
```bash
# Test direct connection (will fail due to CORS)
curl -X GET "https://megha-dev.sirobilt.com/q/api/departments" \
  -H "Content-Type: application/json"

# Test through proxy (should work)
curl -X GET "http://localhost:5173/api/departments" \
  -H "Content-Type: application/json"
```

## 📋 Configuration Files

### **Key Files to Check:**
1. `vite.config.ts` - Proxy configuration
2. `src/config/apiConfig.ts` - API settings and mock data flag
3. `src/services/departmentApis.ts` - API implementations

### **Environment Variables:**
```env
# Development
NODE_ENV=development
VITE_API_BASE_URL=/api

# Production
NODE_ENV=production
VITE_API_BASE_URL=https://api.meghasanjeevini.com/api
```

## 🚀 Next Steps

### **Immediate (Current State):**
- ✅ Mock data is enabled for development
- ✅ Proxy is configured for when CORS is fixed
- ✅ Testing tools are available

### **When Backend CORS is Fixed:**
1. Set `USE_MOCK_DATA: false` in `src/config/apiConfig.ts`
2. Restart development server
3. Test API integration using `/api-test`
4. Verify all CRUD operations work

### **For Production Deployment:**
1. Ensure backend allows requests from production domain
2. Update `BASE_URL` in production environment
3. Set `USE_MOCK_DATA: false`
4. Test thoroughly before deployment

## 🔍 Troubleshooting

### **Common Issues:**
1. **Proxy not working**: Restart development server after config changes
2. **Still getting CORS errors**: Check if using correct URL (should be `/api/...`)
3. **404 errors**: Verify backend endpoints match frontend expectations
4. **Authentication errors**: Ensure JWT tokens are properly stored and sent

### **Debug Steps:**
1. Check browser Network tab for actual requests
2. Verify proxy logs in terminal
3. Test with CORS test component
4. Compare working mock data vs API responses

## 📞 Support

For backend CORS configuration support, contact:
- **Backend Team**: Request CORS headers for `http://localhost:5173`
- **DevOps Team**: For production domain CORS configuration

## 📝 Status

- **Current Status**: Using mock data due to CORS issues
- **Proxy Status**: Configured and ready
- **Backend Integration**: Ready when CORS is resolved
- **Testing Tools**: Available at `/cors-test` and `/api-test`

---

**Last Updated**: December 19, 2024  
**Next Review**: When backend CORS is configured
