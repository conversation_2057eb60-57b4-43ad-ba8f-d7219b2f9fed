@import "tailwindcss";
@plugin "daisyui";

/* Force blur effects with maximum specificity - WORKING SOLUTION */
.blur-backdrop {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  -moz-backdrop-filter: blur(8px) !important;
  -ms-backdrop-filter: blur(8px) !important;
}

.blur-backdrop-light {
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  -moz-backdrop-filter: blur(4px) !important;
  -ms-backdrop-filter: blur(4px) !important;
}

/* Override Tailwind with element specificity */
div.blur-backdrop {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

div.blur-backdrop-light {
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Fallback for unsupported browsers */
@supports not (backdrop-filter: blur(1px)) {
  .blur-backdrop,
  .blur-backdrop-light {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }
}