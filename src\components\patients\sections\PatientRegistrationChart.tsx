import React, { useEffect, useState } from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>s,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    ResponsiveContainer,
    CartesianGrid,
    Label,
} from "recharts";
import axios from "axios";

type Granularity = "day" | "month" | "year";

interface GroupedCount {
    period: string;
    count: number;
}

interface RegistrationResponse {
    granularity: Granularity;
    fromDate: string;
    toDate: string;
    groupedCounts: GroupedCount[];
}

const formatDate = (date: Date) => date.toISOString().split("T")[0];

const getPresetDates = (
    preset: string
): { fromDate: string; toDate: string; granularity: Granularity } => {
    const today = new Date();
    const toDate = formatDate(today);
    let from = new Date();
    let granularity: Granularity = "month";

    switch (preset) {
        case "last_month":
            from.setMonth(today.getMonth() - 1);
            granularity = "day";
            break;
        case "last_3_months":
            from.setMonth(today.getMonth() - 3);
            granularity = "month";
            break;
        case "last_6_months":
            from.setMonth(today.getMonth() - 6);
            granularity = "month";
            break;
        case "this_year":
            from = new Date(today.getFullYear(), 0, 1);
            granularity = "month";
            break;
        case "last_year":
            from = new Date(today.getFullYear() - 1, 0, 1);
            granularity = "month";
            break;
        default:
            from.setMonth(today.getMonth() - 1);
    }

    return {
        fromDate: formatDate(from),
        toDate,
        granularity,
    };
};

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const PatientRegistrationChart: React.FC = () => {
    const [granularity, setGranularity] = useState<Granularity>("month");
    const [fromDate, setFromDate] = useState<string>("");
    const [toDate, setToDate] = useState<string>("");
    const [data, setData] = useState<GroupedCount[]>([]);
    const [loading, setLoading] = useState(false);
    const [preset, setPreset] = useState("last_month");

    useEffect(() => {
        const { fromDate, toDate, granularity } = getPresetDates(preset);
        setFromDate(fromDate);
        setToDate(toDate);
        setGranularity(granularity);
    }, [preset]);

    const fetchAnalytics = async () => {
        try {
            setLoading(true);
            const response = await axios.post<RegistrationResponse>(
                `${BASE_URL}/api/patients/dashboard/registrations`,
                { fromDate, toDate, granularity }
            );

            const rawData = response.data.groupedCounts;
            const filledData = generateCompleteTimeline(fromDate, toDate, granularity, rawData);
            setData(filledData);
        } catch (error) {
            console.error("Error fetching registration data", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (fromDate && toDate) fetchAnalytics();
    }, [fromDate, toDate, granularity]);

    const generateCompleteTimeline = (
        from: string,
        to: string,
        granularity: Granularity,
        raw: GroupedCount[]
    ): GroupedCount[] => {
        const result: GroupedCount[] = [];
        const map = new Map(raw.map((d) => [d.period, d.count]));
        let current = new Date(from);
        const end = new Date(to);

        while (current <= end) {
            const label =
                granularity === "year"
                    ? `${current.getFullYear()}`
                    : granularity === "month"
                        ? `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, "0")}`
                        : formatDate(current);

            result.push({ period: label, count: map.get(label) ?? 0 });

            if (granularity === "year") {
                current.setFullYear(current.getFullYear() + 1);
            } else if (granularity === "month") {
                current.setMonth(current.getMonth() + 1);
            } else {
                current.setDate(current.getDate() + 1);
            }
        }

        return result;
    };

    const formatTooltipLabel = (value: string) => {
        if (granularity === "day") return `Date: ${value}`;
        if (granularity === "month") return `Month: ${value}`;
        return `Year: ${value}`;
    };

    const formatTooltipContent = (value: number) => `${value} patient${value !== 1 ? "s" : ""}`;

    return (
        <div className="bg-white shadow-lg rounded-xl p-6 space-y-6 border border-gray-200">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <h2 className="text-2xl font-bold text-gray-800">
                    📊 Patient Registration Analytics
                </h2>

                <div className="flex gap-3 flex-wrap">
                    <div>
                        <label className="block text-xs text-gray-500 mb-1">Date Filter</label>
                        <select
                            value={preset}
                            onChange={(e) => setPreset(e.target.value)}
                            className="border border-gray-300 px-3 py-1 rounded text-sm"
                        >
                            <option value="last_month">Last Month</option>
                            <option value="last_3_months">Last 3 Months</option>
                            <option value="last_6_months">Last 6 Months</option>
                            <option value="this_year">This Year</option>
                            <option value="last_year">Last Year</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-xs text-gray-500 mb-1">From</label>
                        <input
                            type="date"
                            value={fromDate}
                            onChange={(e) => setFromDate(e.target.value)}
                            className="border px-3 py-1 rounded text-sm"
                        />
                    </div>
                    <div>
                        <label className="block text-xs text-gray-500 mb-1">To</label>
                        <input
                            type="date"
                            value={toDate}
                            onChange={(e) => setToDate(e.target.value)}
                            className="border px-3 py-1 rounded text-sm"
                        />
                    </div>
                </div>
            </div>

            {loading ? (
                <p className="text-gray-500 text-sm">Loading chart...</p>
            ) : (
                // ⬇ Increased height to 400 for better spacing
                <ResponsiveContainer width="100%" height={400}>
                    <BarChart
                        data={data}
                        margin={{ top: 20, right: 30, left: 30, bottom: 50 }} // ⬅ Added bottom margin
                    >
                        <CartesianGrid strokeDasharray="3 3" />

                        <XAxis dataKey="period">
                            <Label
                                value={
                                    granularity === "day"
                                        ? "Date"
                                        : granularity === "month"
                                            ? "Month"
                                            : "Year"
                                }
                                position="insideBottom"
                                offset={-15} // ⬅ Moved label downward a bit
                                style={{ fill: "#2563eb", fontSize: 24 }}
                            />
                        </XAxis>

                        <YAxis allowDecimals={false}>
                            <Label
                                angle={-90}
                                position="insideLeft"
                                style={{ textAnchor: "middle", fill: "#2563eb", fontSize: 24 }}
                            >
                                Patient Count
                            </Label>
                        </YAxis>

                        <Tooltip
                            formatter={(value) => formatTooltipContent(value as number)}
                            labelFormatter={(label) => formatTooltipLabel(label as string)}
                        />

                        <Bar dataKey="count" fill="#3b82f6" radius={[6, 6, 0, 0]} />
                    </BarChart>
                </ResponsiveContainer>
            )}
        </div>
    );
};
