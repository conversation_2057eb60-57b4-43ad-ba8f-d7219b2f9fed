import React, { useState, useEffect } from 'react';
import { Building2, Users, Phone, MapPin, Clock, Plus, Edit, Trash2, Search } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { Select } from '../../commonfields/Select';
import { showSuccess, showError } from '../../utils/toastUtils';
import { getDepartments, getDepartmentStats, createDepartment, updateDepartment, deleteDepartment } from '../../services/departmentApis';
import type { Department, DepartmentStats, DepartmentOperatingHours } from '../../types/department';
import { commonDepartments } from '../../types/department';

interface DepartmentManagementProps {
  facilityId?: string;
}

export const DepartmentManagement: React.FC<DepartmentManagementProps> = ({ facilityId }) => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentStats, setDepartmentStats] = useState<Record<string, DepartmentStats>>({});
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>(true);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    phoneNumber: '',
    email: '',
    location: '',
    isActive: true,
    isEmergencyDepartment: false,
    services: [] as string[]
  });

  useEffect(() => {
    loadDepartments();
  }, [facilityId, filterActive, searchTerm]);

  const loadDepartments = async () => {
    setLoading(true);
    try {
      const response = await getDepartments({
        facilityId,
        isActive: filterActive,
        searchTerm: searchTerm || undefined,
        size: 100
      });

      if (response.success) {
        setDepartments(response.data.results);
        
        // Load stats for each department
        const statsPromises = response.data.results.map(dept => 
          getDepartmentStats(dept.departmentId)
        );
        
        const statsResponses = await Promise.all(statsPromises);
        const statsMap: Record<string, DepartmentStats> = {};
        
        statsResponses.forEach((statsResponse, index) => {
          if (statsResponse.success) {
            const departmentId = response.data.results[index].departmentId;
            statsMap[departmentId] = statsResponse.data;
          }
        });
        
        setDepartmentStats(statsMap);
      } else {
        showError('Failed to load departments');
      }
    } catch (error) {
      console.error('Failed to load departments:', error);
      showError('Failed to load departments');
    } finally {
      setLoading(false);
    }
  };

  const formatOperatingHours = (department: Department) => {
    const workingDays = department.operatingHours.filter(hours => hours.isOperating);
    if (workingDays.length === 0) return 'Closed';
    
    if (department.isEmergencyDepartment) {
      return '24/7 Emergency';
    }
    
    const firstDay = workingDays[0];
    return `${firstDay.startTime.substring(0, 5)} - ${firstDay.endTime.substring(0, 5)}`;
  };

  const getDepartmentStatusColor = (department: Department) => {
    if (!department.isActive) return 'bg-gray-100 text-gray-800';
    if (department.isEmergencyDepartment) return 'bg-red-100 text-red-800';
    return 'bg-green-100 text-green-800';
  };

  const getDepartmentStatusText = (department: Department) => {
    if (!department.isActive) return 'Inactive';
    if (department.isEmergencyDepartment) return 'Emergency';
    return 'Active';
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      phoneNumber: '',
      email: '',
      location: '',
      isActive: true,
      isEmergencyDepartment: false,
      services: []
    });
    setEditingDepartment(null);
  };

  const handleCreateDepartment = () => {
    resetForm();
    setShowForm(true);
  };

  const handleEditDepartment = (department: Department) => {
    setEditingDepartment(department);
    setFormData({
      name: department.name,
      code: department.code,
      description: department.description || '',
      phoneNumber: department.phoneNumber || '',
      email: department.email || '',
      location: department.location || '',
      isActive: department.isActive,
      isEmergencyDepartment: department.isEmergencyDepartment,
      services: department.services || []
    });
    setShowForm(true);
  };

  const handleDeleteDepartment = async (departmentId: string) => {
    if (!confirm('Are you sure you want to delete this department?')) return;

    setLoading(true);
    try {
      const result = await deleteDepartment(departmentId);
      if (result.success) {
        showSuccess('Department deleted successfully');
        loadDepartments();
      } else {
        showError(result.error || 'Failed to delete department');
      }
    } catch (error) {
      console.error('Failed to delete department:', error);
      showError('Failed to delete department');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.code.trim()) {
      showError('Name and code are required');
      return;
    }

    setLoading(true);
    try {
      const defaultOperatingHours: DepartmentOperatingHours[] = [
        { dayOfWeek: "MONDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
        { dayOfWeek: "TUESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
        { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
        { dayOfWeek: "THURSDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
        { dayOfWeek: "FRIDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
        { dayOfWeek: "SATURDAY", isOperating: formData.isEmergencyDepartment, startTime: "08:00:00", endTime: "14:00:00", emergencyHours: formData.isEmergencyDepartment },
        { dayOfWeek: "SUNDAY", isOperating: formData.isEmergencyDepartment, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: formData.isEmergencyDepartment }
      ];

      const departmentData = {
        facilityId: facilityId || 'fac-001',
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        description: formData.description.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        email: formData.email.trim(),
        location: formData.location.trim(),
        operatingHours: defaultOperatingHours,
        services: formData.services,
        isActive: formData.isActive,
        isEmergencyDepartment: formData.isEmergencyDepartment,
        departmentId: `dept-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      let result;
      if (editingDepartment) {
        result = await updateDepartment(editingDepartment.departmentId, departmentData);
      } else {
        result = await createDepartment(departmentData);
      }

      if (result.success) {
        showSuccess(editingDepartment ? 'Department updated successfully' : 'Department created successfully');
        setShowForm(false);
        resetForm();
        loadDepartments();
      } else {
        showError(result.error || 'Failed to save department');
      }
    } catch (error) {
      console.error('Failed to save department:', error);
      showError('Failed to save department');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Department Management</h1>
          <p className="text-gray-600">Manage hospital departments and their configurations</p>
        </div>
        <Button
          onClick={handleCreateDepartment}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <Plus size={16} />
          <span>New Department</span>
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <Input
                type="text"
                placeholder="Search departments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select
              value={filterActive === undefined ? 'all' : filterActive.toString()}
              onChange={(e) => {
                const value = e.target.value;
                setFilterActive(value === 'all' ? undefined : value === 'true');
              }}
              className="w-32"
            >
              <option value="all">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </Select>
            
            <Button
              onClick={loadDepartments}
              disabled={loading}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>

      {/* Departments Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading departments...</p>
        </div>
      ) : departments.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No Departments Found</h3>
          <p className="text-gray-500">Create your first department to get started.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {departments.map((department) => {
            const stats = departmentStats[department.departmentId];
            
            return (
              <div
                key={department.departmentId}
                className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => setSelectedDepartment(department)}
              >
                {/* Department Header */}
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-indigo-100 p-2 rounded-lg">
                        <Building2 className="text-indigo-600" size={20} />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800">{department.name}</h3>
                        <p className="text-sm text-gray-600">{department.code}</p>
                      </div>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDepartmentStatusColor(department)}`}>
                      {getDepartmentStatusText(department)}
                    </span>
                  </div>
                  
                  {department.description && (
                    <p className="mt-3 text-sm text-gray-600">{department.description}</p>
                  )}
                </div>

                {/* Department Info */}
                <div className="p-6 space-y-4">
                  {/* Location */}
                  {department.location && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin size={14} />
                      <span>{department.location}</span>
                    </div>
                  )}

                  {/* Phone */}
                  {department.phoneNumber && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Phone size={14} />
                      <span>{department.phoneNumber}</span>
                    </div>
                  )}

                  {/* Operating Hours */}
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock size={14} />
                    <span>{formatOperatingHours(department)}</span>
                  </div>

                  {/* Statistics */}
                  {stats && (
                    <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1">
                          <Users size={14} className="text-gray-400" />
                          <span className="text-lg font-semibold text-gray-800">{stats.activeProviders}</span>
                        </div>
                        <p className="text-xs text-gray-600">Active Providers</p>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-800">{stats.currentQueueLength}</div>
                        <p className="text-xs text-gray-600">In Queue</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditDepartment(department);
                    }}
                    className="text-indigo-600 hover:text-indigo-800"
                  >
                    <Edit size={14} />
                  </Button>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteDepartment(department.departmentId);
                    }}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Department Details Modal */}
      {selectedDepartment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 blur-backdrop flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {selectedDepartment.name} Department
                </h2>
                <Button
                  onClick={() => setSelectedDepartment(null)}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  ×
                </Button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Department Code</label>
                  <p className="text-gray-900">{selectedDepartment.code}</p>
                </div>
                
                {selectedDepartment.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Description</label>
                    <p className="text-gray-900">{selectedDepartment.description}</p>
                  </div>
                )}
                
                {selectedDepartment.location && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Location</label>
                    <p className="text-gray-900">{selectedDepartment.location}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-sm font-medium text-gray-700">Services</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedDepartment.services.map((service, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Department Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 blur-backdrop flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {editingDepartment ? 'Edit Department' : 'Create New Department'}
                </h2>
                <Button
                  onClick={() => {
                    setShowForm(false);
                    resetForm();
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  ×
                </Button>
              </div>
            </div>

            <form onSubmit={handleFormSubmit} className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Department Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Cardiology"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Department Code *
                  </label>
                  <Input
                    type="text"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                    placeholder="e.g., CARD"
                    maxLength={10}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <Input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of the department"
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    placeholder="+91-11-2345-6789"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <Input
                  type="text"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="e.g., 2nd Floor, Block A"
                />
              </div>

              {/* Department Type */}
              <div className="space-y-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isEmergencyDepartment}
                    onChange={(e) => setFormData(prev => ({ ...prev, isEmergencyDepartment: e.target.checked }))}
                    className="form-checkbox h-4 w-4 text-indigo-600"
                  />
                  <span className="text-sm text-gray-700">Emergency Department (24/7 Operations)</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="form-checkbox h-4 w-4 text-indigo-600"
                  />
                  <span className="text-sm text-gray-700">Active Department</span>
                </label>
              </div>

              {/* Quick Setup from Common Departments */}
              {!editingDepartment && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Setup (Optional)
                  </label>
                  <Select
                    value=""
                    onChange={(e) => {
                      const selected = commonDepartments.find(d => d.code === e.target.value);
                      if (selected) {
                        setFormData(prev => ({
                          ...prev,
                          name: selected.name,
                          code: selected.code,
                          description: selected.description
                        }));
                      }
                    }}
                  >
                    <option value="">Select from common departments...</option>
                    {commonDepartments.map(dept => (
                      <option key={dept.code} value={dept.code}>
                        {dept.name} ({dept.code}) - {dept.description}
                      </option>
                    ))}
                  </Select>
                </div>
              )}

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                <Button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    resetForm();
                  }}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
                >
                  {loading ? 'Saving...' : editingDepartment ? 'Update Department' : 'Create Department'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};
