import React, { useState } from 'react';
import { Button } from '../../commonfields/Button';
import { showSuccess, showError, showInfo } from '../../utils/toastUtils';
import apiConfig from '../../config/apiConfig';

export const CorsTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addTestResult = (test: string, success: boolean, details: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      details,
      timestamp: new Date().toISOString()
    }]);
  };

  const testDirectConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('https://megha-dev.sirobilt.com/q/api/departments', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      addTestResult('Direct Connection', response.ok, {
        status: response.status,
        statusText: response.statusText,
        url: 'https://megha-dev.sirobilt.com/q/api/departments'
      });
      
      if (response.ok) {
        showSuccess('Direct connection successful');
      } else {
        showError(`Direct connection failed: ${response.status}`);
      }
    } catch (error) {
      addTestResult('Direct Connection', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
        url: 'https://megha-dev.sirobilt.com/q/api/departments'
      });
      showError('Direct connection failed - CORS issue');
    } finally {
      setLoading(false);
    }
  };

  const testProxyConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/departments', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      addTestResult('Proxy Connection', response.ok, {
        status: response.status,
        statusText: response.statusText,
        url: '/api/departments'
      });
      
      if (response.ok) {
        showSuccess('Proxy connection successful');
      } else {
        showError(`Proxy connection failed: ${response.status}`);
      }
    } catch (error) {
      addTestResult('Proxy Connection', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
        url: '/api/departments'
      });
      showError('Proxy connection failed');
    } finally {
      setLoading(false);
    }
  };

  const testConfiguredConnection = async () => {
    setLoading(true);
    try {
      const url = `${apiConfig.BASE_URL}/departments`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      addTestResult('Configured Connection', response.ok, {
        status: response.status,
        statusText: response.statusText,
        url,
        baseUrl: apiConfig.BASE_URL
      });
      
      if (response.ok) {
        showSuccess('Configured connection successful');
      } else {
        showError(`Configured connection failed: ${response.status}`);
      }
    } catch (error) {
      addTestResult('Configured Connection', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
        url: `${apiConfig.BASE_URL}/departments`,
        baseUrl: apiConfig.BASE_URL
      });
      showError('Configured connection failed');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const restartDevServer = () => {
    showInfo('Please restart the development server (npm run dev) for proxy changes to take effect');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          🔧 CORS & API Connection Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Current Configuration
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Base URL:</strong> {apiConfig.BASE_URL}
            </div>
            <div>
              <strong>Environment:</strong> {import.meta.env.DEV ? 'Development' : 'Production'}
            </div>
            <div>
              <strong>Mock Data:</strong> {apiConfig.USE_MOCK_DATA ? 'Enabled' : 'Disabled'}
            </div>
            <div>
              <strong>Proxy Configured:</strong> {import.meta.env.DEV ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Connection Tests
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Button
              onClick={testDirectConnection}
              disabled={loading}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Direct (CORS)'}
            </Button>
            
            <Button
              onClick={testProxyConnection}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Proxy'}
            </Button>
            
            <Button
              onClick={testConfiguredConnection}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Configured'}
            </Button>
            
            <Button
              onClick={clearResults}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear Results
            </Button>
          </div>

          <Button
            onClick={restartDevServer}
            className="w-full px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 mb-4"
          >
            📝 Restart Dev Server (for proxy changes)
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test Results
            </h2>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.success 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className={`font-medium ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.test}
                    </h3>
                    <span className={`text-sm ${
                      result.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {result.success ? '✅ Success' : '❌ Failed'}
                    </span>
                  </div>
                  <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                  <div className="text-xs text-gray-500 mt-2">
                    {new Date(result.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-blue-800 font-medium mb-2">🔧 CORS Solutions:</h3>
          <div className="text-blue-700 text-sm space-y-2">
            <p><strong>1. Proxy (Recommended):</strong> Use Vite proxy configuration (already configured)</p>
            <p><strong>2. Backend CORS:</strong> Ask backend team to add CORS headers for localhost:5173</p>
            <p><strong>3. Browser Extension:</strong> Use CORS browser extension for development</p>
            <p><strong>4. Mock Data:</strong> Set USE_MOCK_DATA = true in apiConfig.ts</p>
          </div>
        </div>

        <div className="mt-6 text-center">
          <Button
            onClick={() => window.location.href = '/api-test'}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 mr-4"
          >
            ← Back to API Test
          </Button>
          <Button
            onClick={() => window.location.href = '/'}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            ← Back to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};
