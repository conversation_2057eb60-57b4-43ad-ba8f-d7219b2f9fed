<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Management API Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .toc {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .toc h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin-bottom: 8px;
        }
        
        .toc a {
            color: #333;
            text-decoration: none;
            padding: 5px 0;
            display: block;
            border-left: 3px solid transparent;
            padding-left: 15px;
            transition: all 0.3s ease;
        }
        
        .toc a:hover {
            color: #667eea;
            border-left-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .section {
            background: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #333;
            font-size: 1.3rem;
            margin: 25px 0 15px 0;
        }
        
        .section h4 {
            color: #555;
            font-size: 1.1rem;
            margin: 20px 0 10px 0;
        }
        
        .endpoint {
            background: #f8f9ff;
            border: 1px solid #e1e5f2;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9rem;
            margin-right: 10px;
        }
        
        .method.get { background: #e3f2fd; color: #1976d2; }
        .method.post { background: #e8f5e8; color: #388e3c; }
        .method.put { background: #fff3e0; color: #f57c00; }
        .method.delete { background: #ffebee; color: #d32f2f; }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 15px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5f2;
        }
        
        th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f8f9ff;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge.success { background: #e8f5e8; color: #388e3c; }
        .badge.warning { background: #fff3e0; color: #f57c00; }
        .badge.error { background: #ffebee; color: #d32f2f; }
        .badge.info { background: #e3f2fd; color: #1976d2; }
        
        .download-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        
        .download-btn {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #e1e5f2;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }
            
            .code-block {
                font-size: 0.8rem;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏥 Department Management API</h1>
            <p>Comprehensive API Documentation for MeghaSanjeevini Hospital Management System</p>
            <p><strong>Version 1.0</strong> | December 2024</p>
        </div>

        <!-- Download Section -->
        <div class="download-section">
            <h2>📥 Download Documentation</h2>
            <p>Get the complete API documentation in your preferred format</p>
            <a href="Department_Management_API_Documentation.md" class="download-btn" download>
                📄 Download Markdown
            </a>
            <a href="Department_Management_API_Documentation.html" class="download-btn" download>
                🌐 Download HTML
            </a>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h2>📋 Table of Contents</h2>
            <ul>
                <li><a href="#overview">🎯 Overview</a></li>
                <li><a href="#database-schema">🗄️ Database Schema</a></li>
                <li><a href="#department-apis">📋 Department Management APIs</a></li>
                <li><a href="#mapping-apis">👥 Provider-Department Mapping APIs</a></li>
                <li><a href="#queue-apis">🏥 Queue Management APIs</a></li>
                <li><a href="#data-models">📊 Data Models</a></li>
                <li><a href="#authentication">🔐 Authentication & Authorization</a></li>
                <li><a href="#error-handling">⚠️ Error Handling</a></li>
                <li><a href="#implementation">🛠️ Implementation Guidelines</a></li>
            </ul>
        </div>

        <!-- Overview Section -->
        <div class="section" id="overview">
            <h2>🎯 Overview</h2>
            <p>This comprehensive API documentation provides all the necessary endpoints and database schema for implementing the Department Management and Provider-Department Mapping system in the MeghaSanjeevini Hospital Management System.</p>
            
            <h3>Key Features</h3>
            <ul>
                <li>✅ Complete CRUD operations for hospital departments</li>
                <li>✅ Provider-department relationship management with roles</li>
                <li>✅ Real-time queue tracking by department</li>
                <li>✅ Doctor availability monitoring for OPD</li>
                <li>✅ Role-based access control and permissions</li>
                <li>✅ Comprehensive audit trail for all operations</li>
            </ul>

            <h3>Base URL</h3>
            <div class="code-block">https://api.meghasanjeevini.com/api</div>

            <h3>Authentication</h3>
            <p>All endpoints require Bearer token authentication:</p>
            <div class="code-block">Authorization: Bearer {jwt_token}</div>
        </div>

        <!-- Database Schema Section -->
        <div class="section" id="database-schema">
            <h2>🗄️ Database Schema</h2>
            <p>The following database tables are required for the department management system:</p>

            <h3>Core Tables</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Table Name</th>
                            <th>Purpose</th>
                            <th>Key Relationships</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>departments</strong></td>
                            <td>Store department information</td>
                            <td>facilities, providers (head)</td>
                        </tr>
                        <tr>
                            <td><strong>department_operating_hours</strong></td>
                            <td>Department working hours</td>
                            <td>departments (cascade delete)</td>
                        </tr>
                        <tr>
                            <td><strong>department_services</strong></td>
                            <td>Services offered by departments</td>
                            <td>departments (cascade delete)</td>
                        </tr>
                        <tr>
                            <td><strong>provider_department_mappings</strong></td>
                            <td>Provider-department relationships</td>
                            <td>providers, departments, facilities</td>
                        </tr>
                        <tr>
                            <td><strong>department_stats</strong></td>
                            <td>Cached department statistics</td>
                            <td>departments (cascade delete)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>Sample SQL Schema</h3>
            <div class="code-block">
-- Main departments table
CREATE TABLE departments (
    department_id VARCHAR(50) PRIMARY KEY,
    facility_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) NOT NULL,
    description TEXT,
    head_of_department VARCHAR(50),
    phone_number VARCHAR(20),
    email VARCHAR(100),
    location VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    is_emergency_department BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_facility_code (facility_id, code),
    FOREIGN KEY (facility_id) REFERENCES facilities(facility_id),
    FOREIGN KEY (head_of_department) REFERENCES providers(provider_id)
);

-- Provider-department mappings
CREATE TABLE provider_department_mappings (
    mapping_id VARCHAR(50) PRIMARY KEY,
    provider_id VARCHAR(50) NOT NULL,
    department_id VARCHAR(50) NOT NULL,
    facility_id VARCHAR(50) NOT NULL,
    role ENUM('HEAD', 'SENIOR', 'JUNIOR', 'CONSULTANT', 'RESIDENT', 'INTERN', 'VISITING') NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    effective_from DATE NOT NULL,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (facility_id) REFERENCES facilities(facility_id)
);
            </div>
        </div>

        <!-- Department APIs Section -->
        <div class="section" id="department-apis">
            <h2>📋 Department Management APIs</h2>

            <!-- Get All Departments -->
            <div class="endpoint">
                <h3><span class="method get">GET</span> /api/departments</h3>
                <p><strong>Description:</strong> Retrieve all departments with optional filtering</p>
                
                <h4>Query Parameters</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>facilityId</td>
                                <td>string</td>
                                <td>No</td>
                                <td>Filter by facility ID</td>
                            </tr>
                            <tr>
                                <td>isActive</td>
                                <td>boolean</td>
                                <td>No</td>
                                <td>Filter by active status</td>
                            </tr>
                            <tr>
                                <td>searchTerm</td>
                                <td>string</td>
                                <td>No</td>
                                <td>Search in name, code, description</td>
                            </tr>
                            <tr>
                                <td>page</td>
                                <td>number</td>
                                <td>No</td>
                                <td>Page number (default: 0)</td>
                            </tr>
                            <tr>
                                <td>size</td>
                                <td>number</td>
                                <td>No</td>
                                <td>Page size (default: 20)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h4>Example Request</h4>
                <div class="code-block">
curl -X GET "https://api.meghasanjeevini.com/api/departments?facilityId=fac-001&isActive=true" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
                </div>

                <h4>Response</h4>
                <div class="code-block">
{
  "success": true,
  "data": {
    "results": [
      {
        "departmentId": "dept-001",
        "facilityId": "fac-001",
        "name": "Cardiology",
        "code": "CARD",
        "description": "Heart and cardiovascular diseases",
        "isActive": true,
        "isEmergencyDepartment": false,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalElements": 5,
    "totalPages": 1,
    "currentPage": 0
  }
}
                </div>
            </div>

            <!-- Create Department -->
            <div class="endpoint">
                <h3><span class="method post">POST</span> /api/departments</h3>
                <p><strong>Description:</strong> Create a new department</p>
                
                <h4>Request Body</h4>
                <div class="code-block">
{
  "facilityId": "fac-001",
  "name": "Cardiology",
  "code": "CARD",
  "description": "Heart and cardiovascular diseases",
  "phoneNumber": "+91-11-2345-6789",
  "email": "<EMAIL>",
  "location": "2nd Floor, Block A",
  "isActive": true,
  "isEmergencyDepartment": false
}
                </div>

                <h4>Response</h4>
                <div class="code-block">
{
  "success": true,
  "data": {
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "name": "Cardiology",
    "code": "CARD",
    "description": "Heart and cardiovascular diseases",
    "isActive": true,
    "createdAt": "2024-12-19T10:30:00Z"
  }
}
                </div>
            </div>
        </div>

        <!-- Complete API Reference Note -->
        <div class="section">
            <h2>📖 Complete API Reference</h2>
            <p>This HTML preview shows the key sections of the API documentation. For the complete reference including:</p>
            <ul>
                <li>🔄 All CRUD operations for departments</li>
                <li>👥 Provider-department mapping APIs</li>
                <li>🏥 Queue management endpoints</li>
                <li>📊 Complete data models and schemas</li>
                <li>🔐 Authentication and authorization details</li>
                <li>⚠️ Error handling and status codes</li>
                <li>🛠️ Implementation guidelines and best practices</li>
                <li>🧪 Testing examples and Postman collections</li>
            </ul>
            <p><strong>Please download the complete Markdown documentation using the download button above.</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>MeghaSanjeevini Hospital Management System</strong></p>
            <p>Department Management API Documentation v1.0</p>
            <p>Last Updated: December 19, 2024</p>
        </div>
    </div>
</body>
</html>
