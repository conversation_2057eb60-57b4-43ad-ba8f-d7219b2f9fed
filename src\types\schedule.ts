import { DayOfWeek, SlotDuration } from "./appointmentenums";

// Doctor Schedule Configuration Interface
export interface DoctorSchedule {
  scheduleId?: string;
  consultantId: string;
  daysOfWeek: DayOfWeek[];
  startTime: string; // HH:MM:SS format
  endTime: string; // HH:MM:SS format
  slotDuration: SlotDuration;
  effectiveFrom: string; // YYYY-MM-DD format
  effectiveTo: string; // YYYY-MM-DD format
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}

// Schedule Configuration Payload for API
export interface ScheduleConfigPayload {
  consultantId: string;
  daysOfWeek: string[]; // ["MONDAY", "TUESDAY", etc.]
  startTime: string; // "09:00:00"
  endTime: string; // "17:00:00"
  slotDuration: number; // 30
  effectiveFrom: string; // "2025-06-18"
  effectiveTo: string; // "2026-06-18"
}

// Slot Generation Request
export interface SlotGenerationRequest {
  from: string; // "2025-06-19"
  to: string; // "2025-07-20"
  providerId?: string; // optional - if not provided, generates for all providers
}

// Slot Generation Response
export interface SlotGenerationResponse {
  success: boolean;
  message: string;
  data: {
    totalSlotsGenerated: number;
    dateRange: {
      from: string;
      to: string;
    };
    providersAffected: string[];
    conflicts?: SlotConflict[];
  };
}

// Slot Conflict Information
export interface SlotConflict {
  providerId: string;
  providerName: string;
  date: string;
  time: string;
  conflictType: 'EXISTING_APPOINTMENT' | 'OVERLAPPING_SCHEDULE' | 'BLOCKED_TIME';
  details: string;
}

// Available Time Slot for Display
export interface AvailableSlot {
  slotId?: string;
  providerId: string;
  date: string; // YYYY-MM-DD
  startTime: string; // HH:MM:SS
  endTime: string; // HH:MM:SS
  duration: SlotDuration;
  isAvailable: boolean;
  isBooked: boolean;
  appointmentId?: string;
  isBlocked?: boolean;
  blockReason?: string;
}

// Schedule Filters for Admin Interface
export interface ScheduleFilters {
  consultantId?: string;
  dayOfWeek?: DayOfWeek;
  isActive?: boolean;
  effectiveFrom?: string;
  effectiveTo?: string;
  page?: number;
  size?: number;
}

// Schedule Statistics
export interface ScheduleStats {
  totalSchedules: number;
  activeSchedules: number;
  providersWithSchedules: number;
  totalSlotsGenerated: number;
  availableSlots: number;
  bookedSlots: number;
  upcomingScheduleChanges: number;
}

// Schedule Validation Result
export interface ScheduleValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  conflicts: ScheduleConflict[];
}

// Schedule Conflict Details
export interface ScheduleConflict {
  conflictType: 'TIME_OVERLAP' | 'DATE_OVERLAP' | 'PROVIDER_CONFLICT';
  existingScheduleId: string;
  conflictDetails: string;
  suggestedResolution?: string;
}

// Bulk Schedule Operation
export interface BulkScheduleOperation {
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'ACTIVATE' | 'DEACTIVATE';
  scheduleIds: string[];
  reason?: string;
}

// Schedule Template for Quick Setup
export interface ScheduleTemplate {
  templateId?: string;
  name: string;
  description: string;
  daysOfWeek: DayOfWeek[];
  startTime: string;
  endTime: string;
  slotDuration: SlotDuration;
  isDefault?: boolean;
  createdBy?: string;
  createdAt?: string;
}

// Working Day Configuration
export interface WorkingDayConfig {
  dayOfWeek: DayOfWeek;
  isWorkingDay: boolean;
  startTime: string;
  endTime: string;
  breakStartTime?: string;
  breakEndTime?: string;
  slotDuration: SlotDuration;
  maxAppointments?: number;
  specialNotes?: string;
}

// Provider Schedule Summary
export interface ProviderScheduleSummary {
  providerId: string;
  providerName: string;
  totalSchedules: number;
  activeSchedules: number;
  workingDays: DayOfWeek[];
  nextAvailableSlot?: string;
  totalSlotsThisWeek: number;
  bookedSlotsThisWeek: number;
  availabilityPercentage: number;
}

// Export all types for easier importing
export type {
  DoctorSchedule as DoctorScheduleType,
  ScheduleConfigPayload as ScheduleConfigPayloadType,
  SlotGenerationRequest as SlotGenerationRequestType,
  SlotGenerationResponse as SlotGenerationResponseType,
  AvailableSlot as AvailableSlotType,
  ScheduleFilters as ScheduleFiltersType,
  ScheduleStats as ScheduleStatsType
};
