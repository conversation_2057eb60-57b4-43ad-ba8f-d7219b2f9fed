import React, { useState } from 'react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { showSuccess, showError } from '../../utils/toastUtils';
import { getDepartments, createDepartment, getDepartmentById } from '../../services/departmentApis';
import apiConfig from '../../config/apiConfig';

export const ApiIntegrationTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [testDepartmentId, setTestDepartmentId] = useState('');

  const testGetDepartments = async () => {
    setLoading(true);
    try {
      const response = await getDepartments({ facilityId: 'fac-001', size: 5 });
      setResults(response);
      if (response.success) {
        showSuccess('Successfully fetched departments');
      } else {
        showError(response.error || 'Failed to fetch departments');
      }
    } catch (error) {
      console.error('Test failed:', error);
      showError('Test failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const testGetDepartmentById = async () => {
    if (!testDepartmentId.trim()) {
      showError('Please enter a department ID');
      return;
    }

    setLoading(true);
    try {
      const response = await getDepartmentById(testDepartmentId);
      setResults(response);
      if (response.success) {
        showSuccess('Successfully fetched department');
      } else {
        showError(response.error || 'Failed to fetch department');
      }
    } catch (error) {
      console.error('Test failed:', error);
      showError('Test failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const testCreateDepartment = async () => {
    setLoading(true);
    try {
      const testDepartment = {
        facilityId: 'fac-001',
        name: 'Test Department',
        code: 'TEST',
        description: 'Test department created from frontend',
        isActive: true,
        isEmergencyDepartment: false,
        operatingHours: [
          {
            dayOfWeek: 'MONDAY',
            isOperating: true,
            startTime: '09:00:00',
            endTime: '17:00:00'
          }
        ],
        services: ['Test Service']
      };

      const response = await createDepartment(testDepartment);
      setResults(response);
      if (response.success) {
        showSuccess('Successfully created department');
      } else {
        showError(response.error || 'Failed to create department');
      }
    } catch (error) {
      console.error('Test failed:', error);
      showError('Test failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const testApiConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch(apiConfig.BASE_URL + '/health', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json().catch(() => ({ status: 'unknown' }));
      setResults({ 
        success: response.ok, 
        status: response.status, 
        data,
        url: apiConfig.BASE_URL 
      });
      
      if (response.ok) {
        showSuccess('API connection successful');
      } else {
        showError(`API connection failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setResults({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Connection failed',
        url: apiConfig.BASE_URL 
      });
      showError('Connection test failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          🔗 API Integration Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            API Configuration
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Base URL:</strong> {apiConfig.BASE_URL}
            </div>
            <div>
              <strong>Mock Data:</strong> {apiConfig.USE_MOCK_DATA ? 'Enabled' : 'Disabled'}
            </div>
            <div>
              <strong>Environment:</strong> {process.env.NODE_ENV}
            </div>
            <div>
              <strong>Timeout:</strong> {apiConfig.REQUEST_CONFIG.timeout}ms
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            API Tests
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Button
              onClick={testApiConnection}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test API Connection'}
            </Button>
            
            <Button
              onClick={testGetDepartments}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Get Departments'}
            </Button>
            
            <div className="flex space-x-2">
              <Input
                type="text"
                placeholder="Department ID"
                value={testDepartmentId}
                onChange={(e) => setTestDepartmentId(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={testGetDepartmentById}
                disabled={loading || !testDepartmentId.trim()}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                Get By ID
              </Button>
            </div>
            
            <Button
              onClick={testCreateDepartment}
              disabled={loading}
              className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Create Department'}
            </Button>
          </div>
        </div>

        {results && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test Results
            </h2>
            <div className={`p-4 rounded-lg ${results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className="mb-2">
                <strong>Status:</strong> 
                <span className={`ml-2 ${results.success ? 'text-green-800' : 'text-red-800'}`}>
                  {results.success ? 'Success' : 'Failed'}
                </span>
              </div>
              <pre className="text-sm bg-gray-100 p-3 rounded overflow-auto max-h-96">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-yellow-800 font-medium mb-2">🔧 Integration Notes:</h3>
          <div className="text-yellow-700 text-sm space-y-1">
            <p>• <strong>Authentication:</strong> Make sure you have a valid JWT token in localStorage or sessionStorage</p>
            <p>• <strong>CORS:</strong> Ensure the backend allows requests from this domain</p>
            <p>• <strong>Network:</strong> Check browser network tab for detailed error information</p>
            <p>• <strong>Mock Data:</strong> Toggle USE_MOCK_DATA in apiConfig.ts for development</p>
          </div>
        </div>

        <div className="mt-6 text-center">
          <Button
            onClick={() => window.location.href = '/'}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            ← Back to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};
