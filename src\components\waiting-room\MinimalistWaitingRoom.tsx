import React, { useState, useEffect } from 'react';
import { Clock, Calendar, User, MapPin } from 'lucide-react';

interface Doctor {
  id: string;
  name: string;
  department: string;
  specialization: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  status: 'Available' | 'Busy' | 'Break' | 'Offline';
  room?: string;
}

interface Department {
  id: string;
  name: string;
  code: string;
  color: string;
  doctors: Doctor[];
}

// Mock data for today's doctors
const mockDepartments: Department[] = [
  {
    id: '1',
    name: 'Cardiology',
    code: 'CARD',
    color: 'bg-red-500',
    doctors: [
      {
        id: '1',
        name: 'Dr. <PERSON>',
        department: 'Cardiology',
        specialization: 'Interventional Cardiologist',
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        status: 'Available',
        room: 'Room 201'
      },
      {
        id: '2',
        name: 'Dr. <PERSON><PERSON>',
        department: 'Cardiology',
        specialization: 'Cardiac Surgeon',
        startTime: '10:00',
        endTime: '16:00',
        isAvailable: true,
        status: 'Busy',
        room: 'Room 202'
      }
    ]
  },
  {
    id: '2',
    name: 'Orthopedics',
    code: 'ORTH',
    color: 'bg-blue-500',
    doctors: [
      {
        id: '3',
        name: 'Dr. Amit Singh',
        department: 'Orthopedics',
        specialization: 'Joint Replacement Specialist',
        startTime: '08:30',
        endTime: '16:30',
        isAvailable: true,
        status: 'Available',
        room: 'Room 301'
      },
      {
        id: '4',
        name: 'Dr. Neha Gupta',
        department: 'Orthopedics',
        specialization: 'Sports Medicine',
        startTime: '11:00',
        endTime: '18:00',
        isAvailable: true,
        status: 'Break',
        room: 'Room 302'
      }
    ]
  },
  {
    id: '3',
    name: 'Pediatrics',
    code: 'PEDS',
    color: 'bg-green-500',
    doctors: [
      {
        id: '5',
        name: 'Dr. Sunita Patel',
        department: 'Pediatrics',
        specialization: 'Child Development',
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        status: 'Available',
        room: 'Room 101'
      },
      {
        id: '6',
        name: 'Dr. Ravi Mehta',
        department: 'Pediatrics',
        specialization: 'Pediatric Surgery',
        startTime: '14:00',
        endTime: '20:00',
        isAvailable: false,
        status: 'Offline',
        room: 'Room 102'
      }
    ]
  },
  {
    id: '4',
    name: 'Neurology',
    code: 'NEUR',
    color: 'bg-purple-500',
    doctors: [
      {
        id: '7',
        name: 'Dr. Vikram Joshi',
        department: 'Neurology',
        specialization: 'Neurologist',
        startTime: '10:00',
        endTime: '18:00',
        isAvailable: true,
        status: 'Available',
        room: 'Room 401'
      }
    ]
  },
  {
    id: '5',
    name: 'Emergency Medicine',
    code: 'EMRG',
    color: 'bg-red-600',
    doctors: [
      {
        id: '8',
        name: 'Dr. Anita Roy',
        department: 'Emergency Medicine',
        specialization: 'Emergency Physician',
        startTime: '00:00',
        endTime: '23:59',
        isAvailable: true,
        status: 'Available',
        room: 'Emergency Ward'
      },
      {
        id: '9',
        name: 'Dr. Suresh Kumar',
        department: 'Emergency Medicine',
        specialization: 'Trauma Specialist',
        startTime: '08:00',
        endTime: '20:00',
        isAvailable: true,
        status: 'Busy',
        room: 'Trauma Bay 1'
      }
    ]
  },
  {
    id: '6',
    name: 'Gynecology',
    code: 'GYNE',
    color: 'bg-pink-500',
    doctors: [
      {
        id: '10',
        name: 'Dr. Kavita Agarwal',
        department: 'Gynecology',
        specialization: 'Obstetrician & Gynecologist',
        startTime: '09:30',
        endTime: '17:30',
        isAvailable: true,
        status: 'Available',
        room: 'Room 501'
      }
    ]
  }
];

export const MinimalistWaitingRoom: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (time: string) => {
    if (time === '00:00' && time === '23:59') return '24/7';
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Available':
        return 'text-green-500';
      case 'Busy':
        return 'text-yellow-500';
      case 'Break':
        return 'text-orange-500';
      case 'Offline':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status) {
      case 'Available':
        return 'bg-green-500';
      case 'Busy':
        return 'bg-yellow-500';
      case 'Break':
        return 'bg-orange-500';
      case 'Offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const availableDoctors = mockDepartments.flatMap(dept => 
    dept.doctors.filter(doctor => doctor.isAvailable)
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Today's Available Doctors</h1>
            <p className="text-gray-600 mt-1">Operating Hours & Department Information</p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2 text-gray-600 mb-1">
              <Calendar size={20} />
              <span className="text-lg font-medium">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric'
                })}
              </span>
            </div>
            <div className="flex items-center space-x-2 text-gray-900">
              <Clock size={20} />
              <span className="text-xl font-bold">
                {currentTime.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: true
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-4 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl">
                <User className="h-7 w-7 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Total Doctors</p>
                <p className="text-3xl font-bold text-gray-900">
                  {mockDepartments.reduce((total, dept) => total + dept.doctors.length, 0)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-4 bg-gradient-to-br from-green-100 to-green-200 rounded-xl">
                <div className="h-7 w-7 bg-green-500 rounded-full shadow-sm"></div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Available Now</p>
                <p className="text-3xl font-bold text-green-600">{availableDoctors.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-4 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl">
                <MapPin className="h-7 w-7 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Departments</p>
                <p className="text-3xl font-bold text-gray-900">{mockDepartments.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-4 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-xl">
                <Clock className="h-7 w-7 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">24/7 Services</p>
                <p className="text-3xl font-bold text-yellow-600">
                  {mockDepartments.filter(dept =>
                    dept.doctors.some(doc => doc.startTime === '00:00' && doc.endTime === '23:59')
                  ).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Departments Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {mockDepartments.map((department) => (
            <div key={department.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {/* Department Header */}
              <div className={`${department.color} p-6`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-white">{department.name}</h3>
                    <p className="text-white/80 text-sm font-medium">{department.code}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-white">
                      {department.doctors.filter(d => d.isAvailable).length}
                    </div>
                    <div className="text-white/80 text-sm">Available</div>
                  </div>
                </div>
              </div>

              {/* Doctors List */}
              <div className="p-6 space-y-4">
                {department.doctors.map((doctor) => (
                  <div
                    key={doctor.id}
                    className={`p-5 rounded-xl border transition-all duration-200 ${
                      doctor.isAvailable
                        ? 'border-gray-200 bg-gradient-to-r from-gray-50 to-white hover:shadow-sm'
                        : 'border-gray-100 bg-gray-50 opacity-60'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className={`w-4 h-4 rounded-full ${getStatusDot(doctor.status)} shadow-sm`}></div>
                          <h4 className="font-bold text-gray-900 text-lg">{doctor.name}</h4>
                          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
                            doctor.status === 'Available' ? 'bg-green-100 text-green-700' :
                            doctor.status === 'Busy' ? 'bg-yellow-100 text-yellow-700' :
                            doctor.status === 'Break' ? 'bg-orange-100 text-orange-700' :
                            'bg-gray-100 text-gray-500'
                          }`}>
                            {doctor.status}
                          </span>
                        </div>
                        <p className="text-gray-600 mb-3 font-medium">{doctor.specialization}</p>
                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
                            <Clock size={16} className="text-blue-600" />
                            <span className="font-medium">
                              {doctor.startTime === '00:00' && doctor.endTime === '23:59'
                                ? '24/7 Available'
                                : `${formatTime(doctor.startTime)} - ${formatTime(doctor.endTime)}`
                              }
                            </span>
                          </div>
                          {doctor.room && (
                            <div className="flex items-center space-x-2 bg-purple-50 px-3 py-2 rounded-lg">
                              <MapPin size={16} className="text-purple-600" />
                              <span className="font-medium">{doctor.room}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 inline-block">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Available</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span>Busy</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span>On Break</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span>Offline</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
