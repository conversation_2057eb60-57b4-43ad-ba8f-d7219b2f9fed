import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
  type ChangeEvent,
} from "react";
import { useNavigate } from "react-router-dom";
import {
  deletePatientById,
  getPatientById,
  getPatientsPaginated,
} from "../services/patientApis";
import {
  FaEdit,
  FaEye,
  FaIdCard,
  FaPlus,
  FaSearch,
  FaTrashAlt,
  FaUserCheck,
  FaUsers,
} from "react-icons/fa";
import PatientViewModal from "../components/patients/PatientViewModal";
import ConfirmDialog from "../utils/ConfirmDialog";
import { showError, showSuccess } from "../utils/toastUtils";
import { useRoles } from "../hooks/useRoles";

/* ------------------------------------------------------------------ */
/*  Constants & helpers                                               */
/* ------------------------------------------------------------------ */

const PAGE_SIZE = 10;

const debounce = <T extends unknown[]>(
  fn: (...args: T) => void,
  delay = 400
) => {
  let t: ReturnType<typeof setTimeout>;
  return (...args: T) => {
    clearTimeout(t);
    t = setTimeout(() => fn(...args), delay);
  };
};

/** Token-based highlight (order-independent) */
const highlight = (txt: string = "", q: string = "") => {
  const tokens = q.trim().toLowerCase().split(/\s+/).filter(Boolean);
  if (!tokens.length) return txt;

  const escaped = tokens.map((t) =>
    t.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&")
  );
  const re = new RegExp(`(${escaped.join("|")})`, "gi");

  return txt.split(re).map((part, i) =>
    re.test(part) ? (
      <span
        key={i}
        className="bg-yellow-200 px-1 py-0.5 rounded font-semibold text-yellow-900"
      >
        {part}
      </span>
    ) : (
      part
    )
  );
};

/** Strip all whitespace and force-lowercase */
const normalize = (s = "") => s.replace(/\s+/g, "").toLowerCase();

/* ------------------------------------------------------------------ */
/*  Component                                                         */
/* ------------------------------------------------------------------ */

const PatientListPage = () => {
  /* ---------------- state ---------------- */
  const [allPatients, setAllPatients] = useState<any[]>([]); // raw list from API
  const [searchQuery, setSearchQuery] = useState("");
  const [page, setPage] = useState(0);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [showViewModal, setShowViewModal] = useState(false);

  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  const [fullName, setFullName] = useState("");

  const { hasRole } = useRoles();
  const navigate = useNavigate();

  /* ------------- fetch once / on refresh ------------- */
  const loadPatients = useCallback(async () => {
    const res = await getPatientsPaginated({ query: "", page: 1, size: 5000 });
    setAllPatients(res.results ?? res.content ?? []);
    console.log("vvvvvvvvvvvvvvvvvv",res.results);
  }, []);

  useEffect(() => {
    loadPatients();
  }, [loadPatients, refreshTrigger]);

  /* ------------- external “patient:registered” event ------------- */
  useEffect(() => {
    const bump = () => setRefreshTrigger((n) => n + 1);
    window.addEventListener("patient:registered", bump);
    return () => window.removeEventListener("patient:registered", bump);
  }, []);

  /* ---------------- filtering ---------------- */
  const debouncedSetQuery = useRef(
    debounce((q: string) => setSearchQuery(q))
  ).current;

  const onSearchInput = (e: ChangeEvent<HTMLInputElement>) => {
    setPage(0);
    debouncedSetQuery(e.target.value);
  };

  const filteredPatients = useMemo(() => {
    if (!searchQuery.trim()) return allPatients;

    const tokens = searchQuery
      .trim()
      .toLowerCase()
      .split(/\s+/)
      .filter(Boolean);

    return allPatients.filter((p) => {
      const nameParts = [
        p.firstName ?? "",
        p.middleName ?? "",
        p.lastName ?? "",
      ].map((x) => x.toLowerCase());

      const combinedNameNoSpace = nameParts.join("");         // "shivaramgoud"
      const combinedNameWithSpace = nameParts.join(" ");       // "shiva ram goud"

      const phone = normalize(p.contacts?.[0]?.phoneNumber ?? "");
      const email = normalize(p.contacts?.[0]?.email ?? "");
      const idNum = normalize(p.identifierNumber ?? "");
      const abhaAddr = normalize(p.abha?.abhaAddress ?? "");
      const regNo = normalize(p.registrationNumber ?? "");
      const mrn = normalize(p.mrn ?? "");

      // Every token must be found in AT LEAST one of the searchable fields
      return tokens.every((t) =>
        combinedNameNoSpace.includes(t) ||
        combinedNameWithSpace.includes(t) ||
        phone.includes(t) ||
        email.includes(t) ||
        idNum.includes(t) ||
        abhaAddr.includes(t) ||
        regNo.includes(t) ||
        mrn.includes(t)
      );
    });
  }, [allPatients, searchQuery]);

  /* ---------------- pagination ---------------- */
  const totalCount = filteredPatients.length;
  const totalPages = Math.max(Math.ceil(totalCount / PAGE_SIZE), 1);

  const visiblePatients = useMemo(() => {
    const start = page * PAGE_SIZE;
    return filteredPatients.slice(start, start + PAGE_SIZE);
  }, [filteredPatients, page]);

  /* ---------------- actions ---------------- */
  const handleView = async (id: string) => {
    try {
      const res = await getPatientById(id);
      if (Array.isArray(res) && res.length) {
        setSelectedPatient(res[0]);
        setShowViewModal(true);
      } else alert("Patient not found.");
    } catch (err) {
      console.error(err);
      alert("Error fetching patient details.");
    }
  };

  const handleDeleteClick = (id: string) => {
    const p = allPatients.find((x) => x.patientId === id);
    if (!p) return;
    setFullName(
      `${p.firstName} ${p.middleName ? p.middleName + " " : ""}${p.lastName}`
    );
    setPendingDeleteId(id);
    setIsConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!pendingDeleteId) return;
    const ok = await deletePatientById(pendingDeleteId);
    ok
      ? showSuccess("Patient Deleted Successfully", fullName)
      : showError("Failed To Delete Patient", fullName);
    setIsConfirmOpen(false);
    setPendingDeleteId(null);
    loadPatients(); // hard-refresh
  };

  /* ---------------- derived stats ---------------- */
  const activePatientsCount = filteredPatients.filter((p) => p.isActive).length;
  const abhaVerifiedCount = filteredPatients.filter(
    (p) => p.abha?.abhaAddress?.trim()
  ).length;

  /* ---------------- render ---------------- */
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* ----------------------------------------------------------------- */}
        {/* Header                                                            */}
        {/* ----------------------------------------------------------------- */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <h1 className="text-4xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Patient Management
              </h1>
              <p className="text-gray-600 text-lg">
                Manage and monitor patient records efficiently
              </p>
            </div>

            {(hasRole("admin") || hasRole("receptionist")) && (
              <button
                className="group relative inline-flex items-center justify-center px-4 py-3 text-lg font-medium text-white transition-all duration-200 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 hover:scale-105 hover:shadow-xl"
                onClick={() => navigate("/patients")}
              >
                <FaPlus className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform" />
                Register New Patient
                <div className="absolute inset-0 bg-white opacity-20 rounded-xl blur-xl group-hover:opacity-30" />
              </button>
            )}
          </div>
        </div>

        {/* ----------------------------------------------------------------- */}
        {/* Stat cards                                                        */}
        {/* ----------------------------------------------------------------- */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {[
            {
              title: "Total Patients",
              value: totalCount,
              icon: <FaUsers className="w-8 h-8 text-white" />,
              color: "blue",
            },
            {
              title: "Active Patients",
              value: activePatientsCount,
              icon: <FaUserCheck className="w-8 h-8 text-white" />,
              color: "green",
            },
            {
              title: "ABHA Verified",
              value: abhaVerifiedCount,
              icon: <FaIdCard className="w-8 h-8 text-white" />,
              color: "purple",
            },
          ].map(({ title, value, icon, color }) => (
            <div
              key={title}
              className={`group relative overflow-hidden bg-white rounded-2xl shadow-lg border border-gray-100`}
            >
              <div
                className={`absolute inset-0 bg-gradient-to-br from-${color}-500/5 to-${color}-500/5`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">
                      {title}
                    </p>
                    <h3 className="text-3xl font-bold text-gray-900">
                      {value.toLocaleString()}
                    </h3>
                  </div>
                  <div className="relative">
                    <div
                      className={`flex items-center justify-center w-16 h-16 bg-gradient-to-br from-${color}-500 to-${color}-600 rounded-2xl shadow-lg`}
                    >
                      {icon}
                    </div>
                    <div
                      className={`absolute inset-0 bg-${color}-400 rounded-2xl blur-lg opacity-30`}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* ----------------------------------------------------------------- */}
        {/* Search                                                            */}
        {/* ----------------------------------------------------------------- */}
        <div className="bg-white rounded-2xl shadow-lg   p-6 mb-8">
          <div className="relative">
            <FaSearch className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
            <input
              type="text"
              placeholder="Search by firstname, lastname, email, phone number, or ID..."
              className="block w-full pl-12 pr-4 py-4 text-lg
             border border-gray-200 rounded-xl
             focus:outline-none                /* ← remove default outline  */
             focus:border-blue-500             /* blue border               */
             focus:ring-2 focus:ring-blue-500  /* optional blue glow        */
             transition-all bg-gray-50 hover:bg-white focus:bg-white"
              onChange={onSearchInput}
            />

          </div>
        </div>

        {/* ----------------------------------------------------------------- */}
        {/* Table                                                             */}
        {/* ----------------------------------------------------------------- */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full table-fixed divide-y divide-gray-200">
  <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
    <tr>
      {[
        "PatientId",
        "Name",
        "Phone",
        "Email",
        "Abha-Number",
        "Age",
        "Gender",
      ].map((h) => (
        <th
          key={h}
          className="px-4 py-3 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider whitespace-normal break-words"
        >
          {h}
        </th>
      ))}
      <th className="px-4 py-3 text-right text-sm font-semibold text-gray-900 uppercase tracking-wider">
        Actions
      </th>
    </tr>
  </thead>
  <tbody className="divide-y divide-gray-200">
    {visiblePatients.length === 0 ? (
      <tr>
        <td colSpan={8} className="px-6 py-12 text-center">
          <div className="flex flex-col items-center space-y-3">
            <FaUsers className="w-8 h-8 text-gray-400" />
            <p className="text-lg font-medium text-gray-500">No patients found</p>
          </div>
        </td>
      </tr>
    ) : (
      visiblePatients.map((p, idx) => (
        <tr
          key={p.patientId}
          className={`${
            idx % 2 ? "bg-gray-50/50" : "bg-white"
          } hover:bg-gray-50 transition`}
        >
          <td className="px-4 py-4 text-sm text-gray-600 whitespace-normal break-words">
            {p.patientId ?? "N/A"}
          </td>

          {/* name */}
          <td className="px-4 py-4 text-sm text-gray-900 whitespace-normal break-words">
            <div className="flex items-center space-x-4">
              <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {p.firstName?.[0]?.toUpperCase()}
                  {p.lastName?.[0]?.toUpperCase()}
                </span>
              </div>
              <span className="text-sm font-semibold text-gray-900">
                {highlight(`${p.firstName} ${p.middleName || ""} ${p.lastName}`, searchQuery)}
              </span>
            </div>
          </td>

          {/* phone */}
          <td className="px-4 py-4 text-sm text-gray-600 whitespace-normal break-words">
            {highlight(p.contacts?.[0]?.phoneNumber || "N/A", searchQuery)}
          </td>

          {/* email */}
          <td className="px-4 py-4 text-sm text-gray-600 max-w-[10rem] whitespace-normal break-all">
            {highlight(p.contacts?.[0]?.email || "N/A", searchQuery)}
          </td>

          {/* identifier */}
          <td className="px-4 py-4 text-sm text-gray-600 max-w-[20rem] whitespace-normal break-all">
            {p.identifierType === "ABHA"
              ? highlight(p.identifierNumber || "N/A", searchQuery)
              : "N/A"}
          </td>

          {/* age */}
          <td className="px-4 py-4 text-sm text-gray-600 whitespace-normal break-words">
            {p.age ?? "N/A"}
          </td>

          {/* gender */}
          <td className="px-4 py-4">
            <span
              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                p.gender === "Male"
                  ? "bg-blue-100 text-blue-800"
                  : p.gender === "Female"
                  ? "bg-pink-100 text-pink-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {p.gender ?? "N/A"}
            </span>
          </td>

          {/* actions */}
          <td className="px-4 py-4 text-right">
            <div className="flex items-center justify-end space-x-2">
              {(hasRole("admin") || hasRole("doctor") || hasRole("receptionist")) && (
                <button
                  title="View Patient"
                  className="w-10 h-10 flex items-center justify-center text-green-600 bg-green-50 rounded-lg hover:bg-green-100 hover:text-green-700 hover:scale-105 transition"
                  onClick={() => handleView(p.patientId)}
                >
                  <FaEye className="w-4 h-4" />
                </button>
              )}
              {(hasRole("admin") || hasRole("receptionist")) && (
                <button
                  title="Edit Patient"
                  className="w-10 h-10 flex items-center justify-center text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 hover:text-blue-700 hover:scale-105 transition"
                  onClick={() => navigate(`/patients/${p.patientId}`)}
                >
                  <FaEdit className="w-4 h-4" />
                </button>
              )}
              {hasRole("admin") && (
                <button
                  title="Delete Patient"
                  className="w-10 h-10 flex items-center justify-center text-red-600 bg-red-50 rounded-lg hover:bg-red-100 hover:text-red-700 hover:scale-105 transition"
                  onClick={() => handleDeleteClick(p.patientId)}
                >
                  <FaTrashAlt className="w-4 h-4" />
                </button>
              )}
            </div>
          </td>
        </tr>
      ))
    )}
  </tbody>
</table>

          </div>
        </div>

        {/* ----------------------------------------------------------------- */}
        {/* Pagination                                                        */}
        {/* ----------------------------------------------------------------- */}
        <div className="mt-8 flex items-center justify-between">
          {/* mobile arrows */}
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              disabled={page === 0}
              className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              onClick={() => setPage((p) => p - 1)}
            >
              Previous
            </button>
            <button
              disabled={page + 1 >= totalPages}
              className="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              onClick={() => setPage((p) => p + 1)}
            >
              Next
            </button>
          </div>

          {/* desktop pager */}
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <p className="text-sm text-gray-700">
              Showing{" "}
              <span className="font-medium">{page * PAGE_SIZE + 1}</span> to{" "}
              <span className="font-medium">
                {Math.min((page + 1) * PAGE_SIZE, totalCount)}
              </span>{" "}
              of <span className="font-medium">{totalCount}</span> results
            </p>
            <nav
              className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
              aria-label="Pagination"
            >
              <button
                disabled={page === 0}
                className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50"
                onClick={() => setPage((p) => p - 1)}
              >
                Previous
              </button>
              <span className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300">
                Page {page + 1} of {totalPages}
              </span>
              <button
                disabled={page + 1 >= totalPages}
                className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50"
                onClick={() => setPage((p) => p + 1)}
              >
                Next
              </button>
            </nav>
          </div>
        </div>
      </div>

      {/* ----------------------------------------------------------------- */}
      {/* Modals & confirm dialog                                           */}
      {/* ----------------------------------------------------------------- */}
      {showViewModal && selectedPatient && (
        <PatientViewModal
          patient={selectedPatient}
          onClose={() => setShowViewModal(false)}
        />
      )}

      <ConfirmDialog
        isOpen={isConfirmOpen}
        message={`Are you sure you want to delete ${fullName}?`}
        onConfirm={confirmDelete}
        onCancel={() => setIsConfirmOpen(false)}
      />
    </div>
  );
};

export default PatientListPage;
