import React, { useState } from 'react';
import { Calendar, Settings, Play, BarChart3, Eye } from 'lucide-react';
import { But<PERSON> } from '../commonfields/Button';
import { DoctorScheduleAdmin } from '../components/schedule/DoctorScheduleAdmin';
import { SlotGenerationTool } from '../components/schedule/SlotGenerationTool';
import { ConsultantSlotsViewer } from '../components/schedule/ConsultantSlotsViewer';

type TabType = 'schedules' | 'generation' | 'slots' | 'analytics';

const ScheduleManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('schedules');

  const tabs = [
    {
      id: 'schedules' as TabType,
      name: 'Schedule Configuration',
      icon: Settings,
      description: 'Configure doctor schedules and working hours'
    },
    {
      id: 'generation' as TabType,
      name: 'Slot Generation',
      icon: Play,
      description: 'Generate appointment slots from schedules'
    },
    {
      id: 'slots' as TabType,
      name: 'View Slots',
      icon: Eye,
      description: 'View and manage consultant appointment slots'
    },
    {
      id: 'analytics' as TabType,
      name: 'Analytics',
      icon: BarChart3,
      description: 'View schedule and slot analytics'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'schedules':
        return <DoctorScheduleAdmin />;
      case 'generation':
        return <SlotGenerationTool />;
      case 'slots':
        return <ConsultantSlotsViewer />;
      case 'analytics':
        return <ScheduleAnalytics />;
      default:
        return <DoctorScheduleAdmin />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <Calendar className="h-8 w-8 text-indigo-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Schedule Management</h1>
                <p className="text-gray-600">Manage doctor schedules and appointment slots</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm
                    ${activeTab === tab.id
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon size={20} />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto">
        {renderTabContent()}
      </div>
    </div>
  );
};

// Placeholder Analytics Component
const ScheduleAnalytics: React.FC = () => {
  return (
    <div className="p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <BarChart3 className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Schedule Analytics</h3>
        <p className="text-gray-600 mb-6">
          Comprehensive analytics and reporting for schedule management will be available here.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-blue-800 mb-2">Schedule Utilization</h4>
            <p className="text-blue-600">Track how effectively schedules are being used</p>
          </div>
          <div className="bg-green-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-green-800 mb-2">Slot Efficiency</h4>
            <p className="text-green-600">Monitor appointment slot booking rates</p>
          </div>
          <div className="bg-purple-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-purple-800 mb-2">Provider Performance</h4>
            <p className="text-purple-600">Analyze provider availability and productivity</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleManagementPage;
