import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { getDoctorById } from "../../services/doctorApis";
import type { DoctorDTO } from "../../services/doctorApis";

const FieldRow = ({ label, value }: { label: string; value?: string | number | boolean }) => (
  <div className="text-sm space-x-1 break-words">
    <span className="font-semibold text-gray-600">{label}:</span>
    <span className="text-gray-800">{value !== null && value !== undefined && value !== "" ? String(value) : "N/A"}</span>
  </div>
);

const Section = ({
  title,
  children,
  color = "from-gray-50 to-gray-100",
  bar = "from-gray-400 to-gray-600",
}: {
  title: string;
  children: React.ReactNode;
  color?: string;
  bar?: string;
}) => (
  <div className="mt-6 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
    <div className={`bg-gradient-to-r ${color} px-6 py-4 border-b border-gray-200`}>
      <div className="flex items-center space-x-3">
        <div className={`w-2 h-8 bg-gradient-to-b ${bar} rounded-full`}></div>
        <h4 className="text-md font-semibold text-gray-800">{title}</h4>
      </div>
    </div>
    <div className="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">{children}</div>
  </div>
);

interface DoctorProfileModalProps {
  doctorId: string;
  onClose: () => void;
}

const DoctorProfileModal: React.FC<DoctorProfileModalProps> = ({ doctorId, onClose }) => {
  const [doctor, setDoctor] = useState<DoctorDTO | null>(null);
  const [loading, setLoading] = useState(true);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  useEffect(() => {
    const fetchDoctor = async () => {
      setLoading(true);
      const res = await getDoctorById(doctorId);
      if (res.success) {
        setDoctor(res.data);
      } else {
        setErrorMsg(res.error || "Failed to fetch doctor details");
      }
      setLoading(false);
    };

    fetchDoctor();
  }, [doctorId]);

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm flex items-center justify-center">
        <div className="bg-white p-6 rounded-xl shadow-md text-center">Loading...</div>
      </div>
    );
  }

  if (errorMsg) {
    return (
      <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm flex items-center justify-center">
        <div className="bg-white p-6 rounded-xl shadow-md text-center">
          <p className="text-red-600">{errorMsg}</p>
          <button onClick={onClose} className="mt-4 text-blue-600 underline">Close</button>
        </div>
      </div>
    );
  }

  if (!doctor) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm flex justify-center items-center px-4 py-8 overflow-y-auto">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-4xl border border-gray-200 relative max-h-[95vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-600 hover:text-red-500 transition-colors"
          aria-label="Close"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-bold text-gray-800">Doctor Profile</h3>
          <p className="text-sm text-gray-500">Comprehensive details of the selected doctor</p>
        </div>

        {/* Basic Info */}
        <Section title="Basic Information" color="from-indigo-50 to-purple-50" bar="from-indigo-500 to-purple-600">
          <FieldRow label="Full Name" value={doctor.fullName} />
          <FieldRow label="Gender" value={doctor.gender} />
          <FieldRow label="Date of Birth" value={doctor.dateOfBirth} />
          <FieldRow label="Mobile Number" value={doctor.mobileNumber} />
          <FieldRow label="Email" value={doctor.email} />
          <FieldRow label="Registration Number" value={doctor.registrationNumber} />
          <FieldRow label="Registration State" value={doctor.registrationState} />
          <FieldRow label="Years of Experience" value={doctor.yearsOfExperience} />
          <FieldRow label="Telemedicine Ready" value={doctor.telemedicineReady ? "Yes" : "No"} />
          <FieldRow label="Languages Spoken" value={doctor.languagesSpoken?.join(", ")} />
          <FieldRow label="Active" value={doctor.isActive ? "Yes" : "No"} />
        </Section>

        {/* Address Info */}
        <Section title="Address Details" color="from-green-50 to-green-100" bar="from-green-400 to-green-600">
          <FieldRow label="Street" value={doctor.address?.street} />
          <FieldRow label="City" value={doctor.address?.city} />
          <FieldRow label="State" value={doctor.address?.state} />
          <FieldRow label="Postal Code" value={doctor.address?.zipCode} />
          <FieldRow label="Country" value={doctor.address?.country} />
        </Section>

        {/* Mobile Close Button */}
        <div className="text-right mt-8 sm:hidden">
          <button className="bg-gray-100 hover:bg-gray-200 text-sm px-4 py-2 rounded shadow" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DoctorProfileModal;
