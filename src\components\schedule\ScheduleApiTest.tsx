import React, { useState } from 'react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { FormField } from '../../commonfields/FormField';
import { showSuccess, showError } from '../../utils/toastUtils';
import {
  createScheduleConfig,
  getConsultantSlotConfigs,
  updateScheduleConfig
} from '../../services/scheduleApis';
import {
  generateConsultantUUID,
  SAMPLE_CONSULTANT_UUIDS,
  getRandomSampleConsultantUUID,
  isValidConsultantId,
  createFormattedSchedulePayload
} from '../../utils/uuidUtils';
import type { ScheduleConfigPayload, SlotConfigResponse } from '../../types/schedule';

export const ScheduleApiTest: React.FC = () => {
  const [consultantId, setConsultantId] = useState(SAMPLE_CONSULTANT_UUIDS.CARDIOLOGY_CONSULTANT);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  // Sample schedule data for testing with proper UUID format
  const sampleScheduleData: ScheduleConfigPayload = {
    consultantId: consultantId, // Using UUID format like FC415F36-3366-6bf5-A07b-b9E0D4Bddbdd
    daysOfWeek: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
    startTime: '09:00:00.000000000',
    endTime: '17:00:00.000000000',
    slotDuration: 30,
    effectiveFrom: '2024-01-01',
    effectiveTo: '2024-12-31'
  };

  // Helper functions for UUID management
  const generateNewUUID = () => {
    const newUUID = generateConsultantUUID();
    setConsultantId(newUUID);
    showSuccess(`Generated new UUID: ${newUUID}`);
  };

  const useRandomSampleUUID = () => {
    const randomUUID = getRandomSampleConsultantUUID();
    setConsultantId(randomUUID);
    showSuccess(`Using sample UUID: ${randomUUID}`);
  };

  const validateCurrentUUID = () => {
    if (isValidConsultantId(consultantId)) {
      showSuccess('UUID format is valid');
    } else {
      showError('Invalid UUID format. Expected: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX');
    }
  };

  const testGetSchedules = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing GET /api/consultants/{consultantId}/slot-configs');
      const result = await getConsultantSlotConfigs(consultantId);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'GET Schedules',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess(`Found ${result.data?.length || 0} schedule configurations`);
      } else {
        showError(result.error || 'Failed to fetch schedules');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'GET Schedules',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testCreateSchedule = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing POST /api/consultants/{consultantId}/slot-configs');

      // Create formatted payload with UUID validation
      const formattedPayload = createFormattedSchedulePayload(sampleScheduleData);
      console.log('📤 Request payload:', formattedPayload);

      const result = await createScheduleConfig(formattedPayload);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'CREATE Schedule',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess('Schedule created successfully');
      } else {
        showError(result.error || 'Failed to create schedule');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'CREATE Schedule',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testUpdateSchedule = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing PUT /api/consultants/{consultantId}/slot-configs');
      
      // Modified schedule data for update
      const updatedScheduleData: ScheduleConfigPayload = {
        ...sampleScheduleData,
        daysOfWeek: ['MONDAY', 'WEDNESDAY', 'FRIDAY'],
        startTime: '10:00:00.000000000',
        endTime: '16:00:00.000000000',
        slotDuration: 45
      };

      // Create formatted payload with UUID validation
      const formattedPayload = createFormattedSchedulePayload(updatedScheduleData);
      console.log('📤 Request payload:', formattedPayload);

      const result = await updateScheduleConfig(consultantId, formattedPayload);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'UPDATE Schedule',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess('Schedule updated successfully');
      } else {
        showError(result.error || 'Failed to update schedule');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'UPDATE Schedule',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testGetAllSchedules = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing GET /api/consultants/slot-configs (all consultants)');
      const result = await getConsultantSlotConfigs(); // No consultantId = get all
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'GET All Schedules',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess(`Found ${result.data?.length || 0} total schedule configurations`);
      } else {
        showError(result.error || 'Failed to fetch all schedules');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'GET All Schedules',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Schedule Management API Test
        </h2>
        
        <div className="mb-6">
          <FormField label="Consultant ID" required>
            <Input
              value={consultantId}
              onChange={(e) => setConsultantId(e.target.value)}
              placeholder="Enter consultant UUID"
              className="font-mono text-sm"
            />
          </FormField>

          {/* UUID Helper Buttons */}
          <div className="mt-3 flex flex-wrap gap-2">
            <Button
              onClick={generateNewUUID}
              disabled={loading}
              variant="outline"
              className="text-xs"
            >
              Generate New UUID
            </Button>
            <Button
              onClick={useRandomSampleUUID}
              disabled={loading}
              variant="outline"
              className="text-xs"
            >
              Use Sample UUID
            </Button>
            <Button
              onClick={validateCurrentUUID}
              disabled={loading}
              variant="outline"
              className="text-xs"
            >
              Validate UUID
            </Button>
          </div>

          {/* UUID Format Indicator */}
          <div className="mt-2 text-xs">
            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
              isValidConsultantId(consultantId)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isValidConsultantId(consultantId) ? 'Valid UUID Format' : 'Invalid UUID Format'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Button
            onClick={testGetSchedules}
            disabled={loading || !consultantId}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Test GET Specific Consultant'}
          </Button>

          <Button
            onClick={testGetAllSchedules}
            disabled={loading}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test GET All Consultants'}
          </Button>

          <Button
            onClick={testCreateSchedule}
            disabled={loading || !consultantId}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test CREATE Schedule'}
          </Button>

          <Button
            onClick={testUpdateSchedule}
            disabled={loading || !consultantId}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test UPDATE Schedule'}
          </Button>
        </div>

        {results && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Test Results: {results.operation}
            </h3>
            
            <div className={`p-4 rounded-lg border ${
              results.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-2">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  results.success 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {results.success ? 'SUCCESS' : 'FAILED'}
                </span>
              </div>
              
              {results.error && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-red-800">Error:</p>
                  <p className="text-sm text-red-700">{results.error}</p>
                </div>
              )}
              
              {results.data && (
                <div>
                  <p className="text-sm font-medium text-gray-800 mb-2">Response Data:</p>
                  <pre className="text-xs bg-gray-100 p-3 rounded border overflow-auto max-h-64">
                    {JSON.stringify(results.data, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">API Endpoints Being Tested:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <code>GET /api/consultants/{consultantId}/slot-configs</code></li>
              <li>• <code>GET /api/consultants/slot-configs</code></li>
              <li>• <code>POST /api/consultants/{consultantId}/slot-configs</code></li>
              <li>• <code>PUT /api/consultants/{consultantId}/slot-configs</code></li>
            </ul>
          </div>

          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h4 className="font-semibold text-green-900 mb-2">Sample Consultant UUIDs:</h4>
            <div className="text-xs text-green-800 space-y-1">
              <div><strong>Cardiology:</strong> <code>{SAMPLE_CONSULTANT_UUIDS.CARDIOLOGY_CONSULTANT}</code></div>
              <div><strong>Orthopedics:</strong> <code>{SAMPLE_CONSULTANT_UUIDS.ORTHOPEDICS_CONSULTANT}</code></div>
              <div><strong>Pediatrics:</strong> <code>{SAMPLE_CONSULTANT_UUIDS.PEDIATRICS_CONSULTANT}</code></div>
              <div><strong>Neurology:</strong> <code>{SAMPLE_CONSULTANT_UUIDS.NEUROLOGY_CONSULTANT}</code></div>
              <div><strong>Emergency:</strong> <code>{SAMPLE_CONSULTANT_UUIDS.EMERGENCY_CONSULTANT}</code></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
