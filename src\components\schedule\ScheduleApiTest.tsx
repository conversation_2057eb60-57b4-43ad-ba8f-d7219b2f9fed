import React, { useState } from 'react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { FormField } from '../../commonfields/FormField';
import { showSuccess, showError } from '../../utils/toastUtils';
import {
  createScheduleConfig,
  getConsultantSlotConfigs,
  updateScheduleConfig
} from '../../services/scheduleApis';
import type { ScheduleConfigPayload, SlotConfigResponse } from '../../types/schedule';

export const ScheduleApiTest: React.FC = () => {
  const [consultantId, setConsultantId] = useState('08756dae-86dc-4eaf-b1bb-c0d3faba7e39');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  // Sample schedule data for testing
  const sampleScheduleData: ScheduleConfigPayload = {
    consultantId: consultantId,
    daysOfWeek: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
    startTime: '09:00:00.000000000',
    endTime: '17:00:00.000000000',
    slotDuration: 30,
    effectiveFrom: '2024-01-01',
    effectiveTo: '2024-12-31'
  };

  const testGetSchedules = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing GET /api/consultants/{consultantId}/slot-configs');
      const result = await getConsultantSlotConfigs(consultantId);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'GET Schedules',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess(`Found ${result.data?.length || 0} schedule configurations`);
      } else {
        showError(result.error || 'Failed to fetch schedules');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'GET Schedules',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testCreateSchedule = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing POST /api/consultants/{consultantId}/slot-configs');
      console.log('📤 Request payload:', sampleScheduleData);
      
      const result = await createScheduleConfig(sampleScheduleData);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'CREATE Schedule',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess('Schedule created successfully');
      } else {
        showError(result.error || 'Failed to create schedule');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'CREATE Schedule',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testUpdateSchedule = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing PUT /api/consultants/{consultantId}/slot-configs');
      
      // Modified schedule data for update
      const updatedScheduleData: ScheduleConfigPayload = {
        ...sampleScheduleData,
        daysOfWeek: ['MONDAY', 'WEDNESDAY', 'FRIDAY'],
        startTime: '10:00:00.000000000',
        endTime: '16:00:00.000000000',
        slotDuration: 45
      };
      
      console.log('📤 Request payload:', updatedScheduleData);
      
      const result = await updateScheduleConfig(consultantId, updatedScheduleData);
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'UPDATE Schedule',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess('Schedule updated successfully');
      } else {
        showError(result.error || 'Failed to update schedule');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'UPDATE Schedule',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testGetAllSchedules = async () => {
    setLoading(true);
    try {
      console.log('🔍 Testing GET /api/consultants/slot-configs (all consultants)');
      const result = await getConsultantSlotConfigs(); // No consultantId = get all
      
      console.log('📊 API Response:', result);
      setResults({
        operation: 'GET All Schedules',
        success: result.success,
        data: result.data,
        error: result.error
      });

      if (result.success) {
        showSuccess(`Found ${result.data?.length || 0} total schedule configurations`);
      } else {
        showError(result.error || 'Failed to fetch all schedules');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      showError('Test failed');
      setResults({
        operation: 'GET All Schedules',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Schedule Management API Test
        </h2>
        
        <div className="mb-6">
          <FormField label="Consultant ID" required>
            <Input
              value={consultantId}
              onChange={(e) => setConsultantId(e.target.value)}
              placeholder="Enter consultant UUID"
              className="font-mono text-sm"
            />
          </FormField>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Button
            onClick={testGetSchedules}
            disabled={loading || !consultantId}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Test GET Specific Consultant'}
          </Button>

          <Button
            onClick={testGetAllSchedules}
            disabled={loading}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test GET All Consultants'}
          </Button>

          <Button
            onClick={testCreateSchedule}
            disabled={loading || !consultantId}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test CREATE Schedule'}
          </Button>

          <Button
            onClick={testUpdateSchedule}
            disabled={loading || !consultantId}
            className="w-full"
            variant="outline"
          >
            {loading ? 'Testing...' : 'Test UPDATE Schedule'}
          </Button>
        </div>

        {results && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Test Results: {results.operation}
            </h3>
            
            <div className={`p-4 rounded-lg border ${
              results.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center mb-2">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  results.success 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {results.success ? 'SUCCESS' : 'FAILED'}
                </span>
              </div>
              
              {results.error && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-red-800">Error:</p>
                  <p className="text-sm text-red-700">{results.error}</p>
                </div>
              )}
              
              {results.data && (
                <div>
                  <p className="text-sm font-medium text-gray-800 mb-2">Response Data:</p>
                  <pre className="text-xs bg-gray-100 p-3 rounded border overflow-auto max-h-64">
                    {JSON.stringify(results.data, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-2">API Endpoints Being Tested:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <code>GET /api/consultants/{consultantId}/slot-configs</code></li>
            <li>• <code>GET /api/consultants/slot-configs</code></li>
            <li>• <code>POST /api/consultants/{consultantId}/slot-configs</code></li>
            <li>• <code>PUT /api/consultants/{consultantId}/slot-configs</code></li>
          </ul>
        </div>
      </div>
    </div>
  );
};
