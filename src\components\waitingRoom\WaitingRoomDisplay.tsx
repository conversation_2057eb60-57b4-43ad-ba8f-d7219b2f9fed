import React, { useState, useEffect } from 'react';
import { Clock, MapPin, Users, Activity, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import {
  getTodaysDoctorAvailability,
  getDoctorStatus,
  formatTime,
  getStatusColor,
  formatWaitTime,
  type DoctorAvailabilityResponse,
  type DoctorStatusResponse,
  type Doctor,
  type Department
} from '../../services/waitingRoomApis';

interface WaitingRoomDisplayProps {
  facilityId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
}

export const WaitingRoomDisplay: React.FC<WaitingRoomDisplayProps> = ({ 
  facilityId, 
  autoRefresh = true, 
  refreshInterval = 30 
}) => {
  const [availability, setAvailability] = useState<DoctorAvailabilityResponse | null>(null);
  const [doctorStatus, setDoctorStatus] = useState<DoctorStatusResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    console.log("🚀 WaitingRoomDisplay component mounted, loading data...");
    loadWaitingRoomData();

    if (autoRefresh) {
      console.log(`⏰ Setting up auto-refresh every ${refreshInterval} seconds`);
      const interval = setInterval(loadWaitingRoomData, refreshInterval * 1000);
      return () => {
        console.log("🛑 Cleaning up auto-refresh interval");
        clearInterval(interval);
      };
    }
  }, [autoRefresh, refreshInterval]);

  const loadWaitingRoomData = async () => {
    console.log("📡 Starting to load waiting room data...");
    setLoading(true);
    try {
      console.log("🔄 Making API calls (departments from doctors/today response)...");

      // Only call the two required APIs - departments come from availability response
      const [availabilityData, statusData] = await Promise.all([
        getTodaysDoctorAvailability(),
        getDoctorStatus()
      ]);

      console.log("📊 API responses received:", {
        availability: availabilityData,
        status: statusData
      });

      setAvailability(availabilityData);
      setDoctorStatus(statusData);
      setLastUpdated(new Date());

      console.log("✅ Waiting room data loaded successfully");
      console.log("📋 Using departments from availability data:", availabilityData?.departments);
    } catch (error) {
      console.error('❌ Failed to load waiting room data:', error);
      console.error('Error details:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDoctorCurrentStatus = (doctorId: string): string => {
    if (!doctorStatus?.success || !doctorStatus.data.updates) {
      return 'Available'; // Default status
    }
    
    const statusUpdate = doctorStatus.data.updates.find(update => update.doctorId === doctorId);
    return statusUpdate?.status || 'Available';
  };

  const getDoctorWaitTime = (doctorId: string): number | undefined => {
    if (!doctorStatus?.success || !doctorStatus.data.updates) {
      return undefined;
    }
    
    const statusUpdate = doctorStatus.data.updates.find(update => update.doctorId === doctorId);
    return statusUpdate?.estimatedWaitTime;
  };

  if (!availability?.success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-600 mb-2">Waiting Room Display</h2>
          <p className="text-gray-500 mb-4">Unable to load doctor availability</p>
          <Button onClick={loadWaitingRoomData} disabled={loading}>
            {loading ? 'Loading...' : 'Retry'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                {availability.data.facilityName || 'Medical Facility'}
              </h1>
              <p className="text-gray-600">
                Doctor Availability - {new Date(availability.data.date).toLocaleDateString()}
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
              <Button
                onClick={loadWaitingRoomData}
                disabled={loading}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                <span>Refresh</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Doctors</p>
                <p className="text-2xl font-bold text-gray-900">{availability.data.summary.totalDoctors}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Available Now</p>
                <p className="text-2xl font-bold text-gray-900">{availability.data.summary.availableDoctors}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Departments</p>
                <p className="text-2xl font-bold text-gray-900">{availability.data.summary.totalDepartments}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Emergency Services</p>
                <p className="text-2xl font-bold text-gray-900">{availability.data.summary.emergencyServices}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Departments Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {availability.data.departments.map((department) => (
            <div key={department.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* Department Header */}
              <div 
                className="p-4 border-b border-gray-200"
                style={{ backgroundColor: `${department.color}20` }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">{department.name}</h3>
                    <p className="text-sm text-gray-600">{department.code}</p>
                    {department.location && (
                      <p className="text-xs text-gray-500 mt-1">
                        <MapPin size={12} className="inline mr-1" />
                        {department.location}
                      </p>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-600">
                      {department.availableCount}/{department.totalCount}
                    </div>
                    <div className="text-xs text-gray-500">Available</div>
                  </div>
                </div>
              </div>

              {/* Doctors List */}
              <div className="divide-y divide-gray-200">
                {department.doctors.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    No doctors scheduled today
                  </div>
                ) : (
                  department.doctors.map((doctor) => {
                    const currentStatus = getDoctorCurrentStatus(doctor.id);
                    const waitTime = getDoctorWaitTime(doctor.id);
                    
                    return (
                      <div key={doctor.id} className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  {doctor.title} {doctor.firstName} {doctor.lastName}
                                </h4>
                                <p className="text-sm text-gray-600">{doctor.specialization}</p>
                                {doctor.location.room && (
                                  <p className="text-xs text-gray-500">
                                    Room: {doctor.location.room}
                                    {doctor.location.floor && `, Floor ${doctor.location.floor}`}
                                  </p>
                                )}
                              </div>
                            </div>
                            
                            <div className="mt-2 flex items-center space-x-4 text-xs text-gray-600">
                              <div className="flex items-center space-x-1">
                                <Clock size={12} />
                                <span>
                                  {formatTime(doctor.schedule.startTime)} - {formatTime(doctor.schedule.endTime)}
                                </span>
                              </div>
                              {waitTime && (
                                <div>
                                  Wait: {formatWaitTime(waitTime)}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(currentStatus)}`}>
                              {currentStatus}
                            </span>
                            {doctor.availability.currentPatientCount !== undefined && (
                              <div className="text-xs text-gray-500 mt-1">
                                Patients: {doctor.availability.currentPatientCount}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
