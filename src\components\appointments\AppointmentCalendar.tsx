import React, { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Search, Clock } from "lucide-react";
import { useAppointmentStore } from "../../store/appointmentStore";
import { useProviderStore } from "../../store/providerStore";
import { getAppointments } from "../../services/appointmentApis";
import { getProviders } from "../../services/providerApis";
import { AppointmentStatus } from "../../types/appointmentenums";
import type { Appointment } from "../../types/appointment";
import type { Provider } from "../../types/provider";
import { showError } from "../../utils/toastUtils";
import { Button } from "../../commonfields/Button";
import { Input } from "../../commonfields/Input";

interface AppointmentCalendarProps {
  onSelectAppointment?: (appointment: Appointment) => void;
  onSelectSlot?: (slotInfo: { start: Date; end: Date; slots: Date[] }) => void;
  selectedProviderId?: string;
  facilityId?: string;
}

interface TimeSlot {
  time: string;
  hour: number;
  minute: number;
}

interface DayColumn {
  date: Date;
  dayName: string;
  dayNumber: number;
  appointments: Appointment[];
}

export const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({
  onSelectAppointment,
  onSelectSlot,
  selectedProviderId,
  facilityId
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [viewType, setViewType] = useState<'week' | 'month'>('week');

  const { setAppointments: setStoreAppointments } = useAppointmentStore();
  const { setProviders: setStoreProviders } = useProviderStore();

  useEffect(() => {
    loadAppointments();
    loadProviders();
  }, [currentDate, selectedProviderId, facilityId]);

  useEffect(() => {
    if (selectedProviderId && providers.length > 0) {
      const provider = providers.find(p => p.providerId === selectedProviderId);
      setSelectedProvider(provider || null);
    }
  }, [selectedProviderId, providers]);

  const loadAppointments = async () => {
    setLoading(true);
    try {
      // Calculate date range for the week
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      const filters = {
        dateFrom: startOfWeek.toISOString().split('T')[0],
        dateTo: endOfWeek.toISOString().split('T')[0],
        providerId: selectedProviderId,
        facilityId,
        size: 1000
      };

      const response = await getAppointments(filters);
      const appointmentData = response.results || [];

      setAppointments(appointmentData);
      setStoreAppointments(appointmentData);

    } catch (error) {
      console.error("Failed to load appointments:", error);
      showError("Failed to load appointments");
    } finally {
      setLoading(false);
    }
  };

  const loadProviders = async () => {
    try {
      const response = await getProviders({ facilityId, isActive: true, size: 100 });
      const providerData = response.results || [];
      setProviders(providerData);
      setStoreProviders(providerData);
    } catch (error) {
      console.error("Failed to load providers:", error);
    }
  };


  // Helper functions
  const getWeekDays = (): DayColumn[] => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const days: DayColumn[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const dayAppointments = appointments.filter(apt => {
        const aptDate = new Date(apt.appointmentDate);
        return aptDate.toDateString() === date.toDateString();
      });

      days.push({
        date,
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }).toUpperCase(),
        dayNumber: date.getDate(),
        appointments: dayAppointments
      });
    }
    return days;
  };

  const getTimeSlots = (): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    for (let hour = 7; hour <= 18; hour++) {
      slots.push({
        time: `${hour.toString().padStart(2, '0')}:00`,
        hour,
        minute: 0
      });
    }
    return slots;
  };

  const getAppointmentColor = (appointment: Appointment): string => {
    switch (appointment.status) {
      case AppointmentStatus.Scheduled:
        return 'bg-blue-500';
      case AppointmentStatus.Confirmed:
        return 'bg-green-500';
      case AppointmentStatus.InProgress:
        return 'bg-yellow-500';
      case AppointmentStatus.Completed:
        return 'bg-gray-500';
      case AppointmentStatus.Cancelled:
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getAppointmentPosition = (appointment: Appointment): { top: number; height: number } => {
    const startTime = appointment.startTime;
    const endTime = appointment.endTime;

    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    const startMinutes = (startHour - 7) * 60 + startMinute;
    const endMinutes = (endHour - 7) * 60 + endMinute;
    const duration = endMinutes - startMinutes;

    return {
      top: (startMinutes / 60) * 60, // 60px per hour
      height: Math.max((duration / 60) * 60, 30) // Minimum 30px height
    };
  };

  const filteredProviders = providers.filter(provider =>
    provider.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    provider.lastName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentDate(newDate);
  };

  const formatDateRange = (): string => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const year = startOfWeek.getFullYear();

    if (startMonth === endMonth) {
      return `${startOfWeek.getDate()} - ${endOfWeek.getDate()} ${startMonth}, ${year}`;
    } else {
      return `${startOfWeek.getDate()} ${startMonth} - ${endOfWeek.getDate()} ${endMonth}, ${year}`;
    }
  };

  const weekDays = getWeekDays();
  const timeSlots = getTimeSlots();

  return (
    <div className="flex h-full bg-white">
      {/* Left Sidebar - Doctors */}
      <div className="w-80 border-r border-gray-200 flex flex-col">
        {/* Doctors Header */}
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Doctors</h3>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <Input
              placeholder="Search doctors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 rounded-lg"
            />
          </div>
        </div>

        {/* Doctors List */}
        <div className="flex-1 overflow-y-auto">
          {filteredProviders.map((provider) => {
            const providerAppointments = appointments.filter(apt => apt.providerId === provider.providerId);
            const availableSlots = 8 - providerAppointments.length; // Assuming 8 slots per day

            return (
              <div
                key={provider.providerId}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedProvider?.providerId === provider.providerId ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => setSelectedProvider(provider)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <h4 className="font-medium text-gray-900">
                        {provider.title} {provider.firstName} {provider.lastName}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600">{provider.specialization}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">Slots Left: {availableSlots}</p>
                    <p className="text-xs text-gray-500">Earliest Slot: April 25, 3:30 PM</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Main Calendar Area */}
      <div className="flex-1 flex flex-col">
        {/* Calendar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => navigateWeek('prev')}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ChevronLeft size={16} />
                </Button>
                <h2 className="text-lg font-semibold text-gray-900">{formatDateRange()}</h2>
                <Button
                  onClick={() => navigateWeek('next')}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ChevronRight size={16} />
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => setCurrentDate(new Date())}
                className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Today
              </Button>
              <Button
                onClick={() => setViewType('week')}
                className={`px-3 py-1 text-sm rounded-lg ${
                  viewType === 'week' ? 'bg-purple-600 text-white' : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Week
              </Button>
              <Button
                onClick={() => setViewType('month')}
                className={`px-3 py-1 text-sm rounded-lg ${
                  viewType === 'month' ? 'bg-purple-600 text-white' : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                Month
              </Button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="flex-1 overflow-auto">
          <div className="grid grid-cols-8 min-h-full">
            {/* Time Column */}
            <div className="border-r border-gray-200">
              <div className="h-12 border-b border-gray-200"></div>
              {timeSlots.map((slot) => (
                <div key={slot.time} className="h-16 border-b border-gray-100 flex items-start justify-end pr-2 pt-1">
                  <span className="text-xs text-gray-500">{slot.time}</span>
                </div>
              ))}
            </div>

            {/* Day Columns */}
            {weekDays.map((day) => (
              <div key={day.date.toISOString()} className="border-r border-gray-200 relative">
                {/* Day Header */}
                <div className="h-12 border-b border-gray-200 flex flex-col items-center justify-center">
                  <span className="text-xs font-medium text-gray-600">{day.dayName}</span>
                  <span className={`text-lg font-semibold ${
                    day.date.toDateString() === new Date().toDateString()
                      ? 'text-purple-600'
                      : 'text-gray-900'
                  }`}>
                    {day.dayNumber}
                  </span>
                </div>

                {/* Time Slots */}
                <div className="relative">
                  {timeSlots.map((slot) => (
                    <div
                      key={slot.time}
                      className="h-16 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                      onClick={() => {
                        const slotDate = new Date(day.date);
                        slotDate.setHours(slot.hour, slot.minute, 0, 0);
                        const endDate = new Date(slotDate);
                        endDate.setMinutes(endDate.getMinutes() + 30);
                        onSelectSlot?.({ start: slotDate, end: endDate, slots: [slotDate] });
                      }}
                    />
                  ))}

                  {/* Appointments */}
                  {day.appointments.map((appointment) => {
                    const position = getAppointmentPosition(appointment);
                    return (
                      <div
                        key={appointment.appointmentId}
                        className={`absolute left-1 right-1 ${getAppointmentColor(appointment)} text-white text-xs p-1 rounded cursor-pointer hover:opacity-90`}
                        style={{
                          top: `${position.top + 48}px`, // 48px for header
                          height: `${position.height}px`
                        }}
                        onClick={() => onSelectAppointment?.(appointment)}
                      >
                        <div className="font-medium truncate">
                          {appointment.patient?.firstName} {appointment.patient?.lastName}
                        </div>
                        <div className="truncate opacity-90">
                          {appointment.startTime} - {appointment.endTime}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Loading appointments...</p>
          </div>
        </div>
      )}
    </div>
  );
};
