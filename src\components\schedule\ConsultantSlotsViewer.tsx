import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, RefreshCw, Play, AlertCircle } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Select } from '../../commonfields/Select';
import { Calendar as CalendarInput } from '../../commonfields/Calendar';
import { FormField } from '../../commonfields/FormField';
import { showSuccess, showError } from '../../utils/toastUtils';
import {
  getDoctors,
  getConsultantSlots,
  generateConsultantSlots,
  type Doctor,
  type ConsultantSlot,
  type SlotGenerationRequest
} from '../../services/scheduleApis';

interface ConsultantSlotsViewerProps {
  facilityId?: string;
}

export const ConsultantSlotsViewer: React.FC<ConsultantSlotsViewerProps> = ({ facilityId }) => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [slots, setSlots] = useState<ConsultantSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [generationRequest, setGenerationRequest] = useState<SlotGenerationRequest>({
    from: new Date().toISOString().split('T')[0],
    to: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7 days from now
  });

  useEffect(() => {
    loadDoctors();
  }, []);

  useEffect(() => {
    if (selectedDoctorId) {
      loadSlots();
    }
  }, [selectedDoctorId, selectedDate]);

  const loadDoctors = async () => {
    try {
      const response = await getDoctors();
      if (response.success) {
        setDoctors(response.data || []);
      } else {
        showError(response.error || 'Failed to load doctors');
      }
    } catch (error) {
      console.error('Failed to load doctors:', error);
      showError('Failed to load doctors');
    }
  };

  const loadSlots = async () => {
    if (!selectedDoctorId) return;

    setLoading(true);
    try {
      const response = await getConsultantSlots(selectedDoctorId, selectedDate);
      if (response.success) {
        setSlots(response.data || []);
      } else {
        showError(response.error || 'Failed to load slots');
        setSlots([]);
      }
    } catch (error) {
      console.error('Failed to load slots:', error);
      showError('Failed to load slots');
      setSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateSlots = async () => {
    if (!selectedDoctorId) {
      showError('Please select a doctor first');
      return;
    }

    setGenerating(true);
    try {
      const response = await generateConsultantSlots(selectedDoctorId, generationRequest);
      if (response.success) {
        showSuccess(`Successfully generated ${response.data?.slotsGenerated || 0} slots`);
        // Reload slots for the current date if it's within the generated range
        if (selectedDate >= generationRequest.from && selectedDate <= generationRequest.to) {
          loadSlots();
        }
      } else {
        showError(response.error || 'Failed to generate slots');
      }
    } catch (error) {
      console.error('Failed to generate slots:', error);
      showError('Failed to generate slots');
    } finally {
      setGenerating(false);
    }
  };

  const getDoctorName = (doctorId: string) => {
    const doctor = doctors.find(d => d.doctorId === doctorId);
    return doctor ? doctor.fullName : doctorId;
  };

  const formatTime = (timeString: string) => {
    // Convert HH:MM:SS.nnnnnnnnn to HH:MM
    return timeString.split('.')[0].substring(0, 5);
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'OPEN':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'BOOKED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'BLOCKED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Consultant Slots Management</h1>
        <p className="text-gray-600">View and generate appointment slots for consultants</p>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Doctor Selection */}
          <FormField label="Select Doctor" required>
            <Select
              value={selectedDoctorId}
              onChange={(e) => setSelectedDoctorId(e.target.value)}
              required
            >
              <option value="">Choose a doctor</option>
              {doctors.map((doctor) => (
                <option key={doctor.doctorId} value={doctor.doctorId}>
                  {doctor.fullName} - {doctor.specialization.name}
                </option>
              ))}
            </Select>
          </FormField>

          {/* Date Selection */}
          <FormField label="Select Date" required>
            <CalendarInput
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              required
            />
          </FormField>

          {/* Refresh Button */}
          <FormField label="Actions">
            <Button
              onClick={loadSlots}
              disabled={!selectedDoctorId || loading}
              className="w-full flex items-center justify-center space-x-2"
            >
              <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
              <span>Refresh Slots</span>
            </Button>
          </FormField>
        </div>
      </div>

      {/* Slot Generation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Generate New Slots</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* From Date */}
          <FormField label="From Date" required>
            <CalendarInput
              value={generationRequest.from}
              onChange={(e) => setGenerationRequest(prev => ({ ...prev, from: e.target.value }))}
              required
            />
          </FormField>

          {/* To Date */}
          <FormField label="To Date" required>
            <CalendarInput
              value={generationRequest.to}
              onChange={(e) => setGenerationRequest(prev => ({ ...prev, to: e.target.value }))}
              required
            />
          </FormField>

          {/* Generate Button */}
          <FormField label="Generate">
            <Button
              onClick={handleGenerateSlots}
              disabled={!selectedDoctorId || generating}
              className="w-full flex items-center justify-center space-x-2 bg-indigo-600 text-white hover:bg-indigo-700"
            >
              <Play size={16} className={generating ? 'animate-pulse' : ''} />
              <span>{generating ? 'Generating...' : 'Generate Slots'}</span>
            </Button>
          </FormField>
        </div>

        {selectedDoctorId && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              <strong>Selected Doctor:</strong> {getDoctorName(selectedDoctorId)}
            </p>
            <p className="text-blue-700 text-sm">
              <strong>Date Range:</strong> {generationRequest.from} to {generationRequest.to}
            </p>
          </div>
        )}
      </div>

      {/* Slots Display */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-800">
                Slots for {selectedDate}
              </h3>
            </div>
            {selectedDoctorId && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <User className="h-4 w-4" />
                <span>{getDoctorName(selectedDoctorId)}</span>
              </div>
            )}
          </div>
        </div>

        <div className="p-6">
          {!selectedDoctorId ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Please select a doctor to view slots</p>
            </div>
          ) : loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 text-gray-400 mx-auto mb-4 animate-spin" />
              <p className="text-gray-500">Loading slots...</p>
            </div>
          ) : slots.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No slots found for this date</p>
              <p className="text-gray-400 text-sm">Try generating slots for this doctor</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {slots.map((slot, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${getAvailabilityColor(slot.availability)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">Slot #{slot.slotNumber}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(slot.availability)}`}>
                      {slot.availability}
                    </span>
                  </div>
                  <div className="text-sm space-y-1">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span>{formatTime(slot.startTime)} - {formatTime(slot.endTime)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>{slot.slotDate}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
