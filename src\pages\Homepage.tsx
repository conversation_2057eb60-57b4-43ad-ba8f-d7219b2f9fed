import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import {
  <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON><PERSON>hart, Line, XAxis, YA<PERSON>s,
  Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
} from "recharts";
import { CalendarDays, User2, Hospital, HeartPulse, Clock, ArrowRight, Users } from "lucide-react";
import { getPatientsPaginated } from "../services/patientApis";

// Chart colors
const COLORS = ["#34d399", "#60a5fa", "#fbbf24", "#f87171", "#a78bfa"];
const GENDER_COLORS = ["#60a5fa", "#f472b6"];
const AGE_BAR_COLOR = "#6366f1";
const STATUS_COLORS = ["#34d399", "#f87171"];

// Mock queue data
const queueData = [
  { service: "Consultation", waiting: 8, avgWait: "25 min", status: "normal" },
  { service: "Emergency", waiting: 2, avgWait: "5 min", status: "urgent" },
  { service: "Laboratory", waiting: 5, avgWait: "15 min", status: "normal" },
  { service: "Pharmacy", waiting: 3, avgWait: "8 min", status: "good" },
];

// Mock appointments data (you can replace this with real API later)
const appointments = [
  { patient: "John Doe", dept: "Cardiology", doctor: "Dr. Smith", time: "10:00 AM" },
  { patient: "Jane Smith", dept: "Neurology", doctor: "Dr. Taylor", time: "11:30 AM" },
  { patient: "Alice Brown", dept: "ENT", doctor: "Dr. Johnson", time: "1:00 PM" },
];

const StatCard = ({ title, value, icon }: { title: string; value: string | number; icon: React.ReactNode }) => (
  <div className="flex items-center p-5 bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition rounded-2xl border border-gray-200 dark:border-gray-700 gap-4">
    <div className="p-3 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 rounded-full shadow-inner">
      {icon}
    </div>
    <div>
      <div className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</div>
      <div className="text-xl font-bold text-gray-900 dark:text-white">{value}</div>
    </div>
  </div>
);

const Homepage = () => {
  const [patients, setPatients] = useState([]);
  const [period, setPeriod] = useState("6m");

  useEffect(() => {
    fetchPatients("", 0);
  }, []);

  const fetchPatients = async (query: string, currentPage: number) => {
    const result = await getPatientsPaginated({ query, page: currentPage, size: 100 });

    setPatients(result.results || []);
  };

  const abhaVerifiedCount = patients.filter(p => p.abha?.abhaAddress?.trim()).length;
  const activeCount = patients.filter(p => p.isActive).length;
  const inactiveCount = patients.length - activeCount;
  const maleCount = patients.filter(p => p.gender === "Male").length;
  const femaleCount = patients.filter(p => p.gender === "Female").length;

  const averageAge = patients.length
    ? Math.round(patients.reduce((sum, p) => sum + p.age, 0) / patients.length)
    : 0;

  const genderData = [
   
    { name: "Male", value: maleCount },
     { name: "Female", value: femaleCount },
  ];
const ageGroupData = [
  { name: "0–24", count: patients.filter(p => p.age < 25).length },
  { name: "25–34", count: patients.filter(p => p.age >= 25 && p.age < 35).length },
  { name: "35–44", count: patients.filter(p => p.age >= 35 && p.age < 45).length },
  { name: "45–54", count: patients.filter(p => p.age >= 45 && p.age < 55).length },
  { name: "55–64", count: patients.filter(p => p.age >= 55 && p.age < 65).length },
  { name: "65+", count: patients.filter(p => p.age >= 65).length },
];

  const activeStatusData = [
    { name: "Active", value: activeCount },
    { name: "Inactive", value: inactiveCount },
  ];

  const admissionData = [
    { date: "Mon", patients: 30 },
    { date: "Tue", patients: 45 },
    { date: "Wed", patients: 38 },
    { date: "Thu", patients: 50 },
    { date: "Fri", patients: 65 },
  ];

  const performanceData = [
    { month: "Jul", patients: 140, appointments: 240, revenue: 180 },
    { month: "Aug", patients: 135, appointments: 160, revenue: 220 },
    { month: "Sep", patients: 120, appointments: 240, revenue: 240 },
    { month: "Oct", patients: 125, appointments: 210, revenue: 220 },
    { month: "Nov", patients: 130, appointments: 190, revenue: 180 },
    { month: "Dec", patients: 132, appointments: 165, revenue: 150 },
  ];

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-900 dark:to-gray-950 min-h-screen transition-colors">
      <div className="flex items-center mb-8">
        <Hospital className="w-10 h-10 text-indigo-900 dark:text-indigo-400 mr-3" />
        <h1 className="text-4xl font-bold text-indigo-900 dark:text-white">Hospital Dashboard</h1>
      </div>

      {/* Stat Cards */}
      {/* <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <StatCard title="Patients Today" value="93" icon={<User2 size={20} />} />
        <StatCard title="Appointments" value="31" icon={<CalendarDays size={20} />} />
        <StatCard title="In Queue" value="18" icon={<Clock size={20} />} />
        <StatCard title="Available Beds" value="42 / 100" icon={<Hospital size={20} />} />
      </div> */}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-8">
        <StatCard title="Patients Today" value={patients.length} icon={<User2 size={20} />} />
        <StatCard title="ABHA Verified" value={abhaVerifiedCount} icon={<HeartPulse size={20} />} />
        <StatCard title="Active Patients" value={activeCount} icon={<Users size={20} />} />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-md">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100 mb-4">Gender Distribution</h2>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie data={genderData} dataKey="value" nameKey="name" innerRadius={40} outerRadius={60} label>
                {genderData.map((_, i) => (
                  <Cell key={i} fill={GENDER_COLORS[i % GENDER_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-md">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100 mb-4">Age Group Distribution</h2>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={ageGroupData}>
              <XAxis dataKey="name" stroke="#8884d8" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="count" fill={AGE_BAR_COLOR} radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-md">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100 mb-4">Admissions This Week</h2>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={admissionData}>
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="patients" stroke="#4f46e5" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-md">
          <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100 mb-4">Active vs Inactive Patients</h2>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie data={activeStatusData} dataKey="value" nameKey="name" outerRadius={80} label>
                {activeStatusData.map((_, index) => (
                  <Cell key={index} fill={STATUS_COLORS[index % STATUS_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div> */}

      {/* Performance + Queue/Appointments */}
      {/* <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-md mb-10">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100">Hospital Performance</h2>
            <p className="text-sm text-slate-500 dark:text-gray-400">Monthly overview of patients, appointments, and revenue</p>
          </div>
          <select
            className="text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md px-2 py-1"
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
          >
            <option value="1m">Last Month</option>
            <option value="3m">Last 3 Months</option>
            <option value="6m">Last 6 Months</option>
            <option value="1y">Last Year</option>
          </select>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={performanceData}>
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="patients" fill="#3b82f6" name="Patients" />
            <Bar dataKey="appointments" fill="#10b981" name="Appointments" />
            <Bar dataKey="revenue" fill="#f472b6" name="Revenue" />
          </BarChart>
        </ResponsiveContainer>
      </div> */}

      {/* Queue and Appointments */}
      {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">🚶‍♂️ Queue Status</h2>
            <Link to="/queue" className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
              <span>View All</span>
              <ArrowRight size={16} />
            </Link>
          </div>
          <div className="space-y-3">
            {queueData.map((queue, idx) => (
              <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    queue.status === 'urgent' ? 'bg-red-500' :
                    queue.status === 'normal' ? 'bg-yellow-500' : 'bg-green-500'
                  }`}></div>
                  <div>
                    <div className="font-medium text-gray-700">{queue.service}</div>
                    <div className="text-xs text-gray-500">{queue.waiting} patients waiting</div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">{queue.avgWait}</div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Link to="/queue" className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center space-x-2">
              <Users size={16} />
              <span>Manage Queues</span>
            </Link>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">📅 Upcoming Appointments</h2>
            <Link to="/appointments" className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
              <span>View All</span>
              <ArrowRight size={16} />
            </Link>
          </div>
          <div className="space-y-3">
            {appointments.map((a, idx) => (
              <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                <div>
                  <div className="font-medium text-gray-700">{a.patient}</div>
                  <div className="text-xs text-gray-500">{a.dept} — {a.doctor}</div>
                </div>
                <div className="text-sm text-gray-600">{a.time}</div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Link to="/appointments" className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center space-x-2">
              <CalendarDays size={16} />
              <span>Manage Appointments</span>
            </Link>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Homepage;
