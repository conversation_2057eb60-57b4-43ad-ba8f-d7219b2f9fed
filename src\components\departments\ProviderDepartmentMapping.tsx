import React, { useState, useEffect } from 'react';
import { Users, Building2, Plus, Edit, Trash2, Search, UserCheck } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { Input } from '../../commonfields/Input';
import { Select } from '../../commonfields/Select';
import { FormField } from '../../commonfields/FormField';
import { showSuccess, showError } from '../../utils/toastUtils';
import { getDepartments, getProviderDepartmentMappings, createProviderDepartmentMapping, updateProviderDepartmentMapping, deleteProviderDepartmentMapping } from '../../services/departmentApis';
import { getProviders } from '../../services/providerApis';
import type { Department, ProviderDepartmentMapping, DepartmentRole } from '../../types/department';
import { departmentRoleOptions } from '../../types/department';
import type { Provider } from '../../types/provider';

interface ProviderDepartmentMappingProps {
  facilityId?: string;
}

export const ProviderDepartmentMappingComponent: React.FC<ProviderDepartmentMappingProps> = ({ facilityId }) => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [mappings, setMappings] = useState<ProviderDepartmentMapping[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingMapping, setEditingMapping] = useState<ProviderDepartmentMapping | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  
  const [formData, setFormData] = useState({
    providerId: '',
    departmentId: '',
    role: 'CONSULTANT' as DepartmentRole,
    isPrimary: false,
    effectiveFrom: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadInitialData();
  }, [facilityId]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Load departments
      const departmentsResponse = await getDepartments({ facilityId, isActive: true, size: 100 });
      setDepartments(departmentsResponse.data.results || []);

      // Load providers
      const providersResponse = await getProviders({ facilityId, isActive: true, size: 100 });
      setProviders(providersResponse.results || []);

      // Load mappings
      const mappingsResponse = await getProviderDepartmentMappings({ facilityId });
      setMappings(mappingsResponse.data || []);
    } catch (error) {
      console.error('Failed to load initial data:', error);
      showError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMapping = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.providerId || !formData.departmentId) {
      showError('Please select both provider and department');
      return;
    }

    setLoading(true);
    try {
      if (editingMapping) {
        const response = await updateProviderDepartmentMapping(editingMapping.mappingId, {
          role: formData.role,
          isPrimary: formData.isPrimary,
          effectiveFrom: formData.effectiveFrom
        });

        if (response.success) {
          showSuccess('Provider-Department mapping updated successfully');
          setShowForm(false);
          resetForm();
          loadInitialData();
        } else {
          showError('Failed to update mapping');
        }
      } else {
        const response = await createProviderDepartmentMapping({
          providerId: formData.providerId,
          departmentId: formData.departmentId,
          facilityId: facilityId || 'fac-001',
          role: formData.role,
          isPrimary: formData.isPrimary,
          effectiveFrom: formData.effectiveFrom,
          isActive: true
        });

        if (response.success) {
          showSuccess('Provider-Department mapping created successfully');
          setShowForm(false);
          resetForm();
          loadInitialData();
        } else {
          showError('Failed to create mapping');
        }
      }
    } catch (error) {
      console.error('Failed to save mapping:', error);
      showError('Failed to save mapping');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      providerId: '',
      departmentId: '',
      role: 'CONSULTANT' as DepartmentRole,
      isPrimary: false,
      effectiveFrom: new Date().toISOString().split('T')[0]
    });
    setEditingMapping(null);
  };

  const handleEditMapping = (mapping: ProviderDepartmentMapping) => {
    setEditingMapping(mapping);
    setFormData({
      providerId: mapping.providerId,
      departmentId: mapping.departmentId,
      role: mapping.role,
      isPrimary: mapping.isPrimary,
      effectiveFrom: mapping.effectiveFrom
    });
    setShowForm(true);
  };

  const handleDeleteMapping = async (mappingId: string) => {
    if (!confirm('Are you sure you want to delete this provider-department mapping?')) return;

    setLoading(true);
    try {
      const response = await deleteProviderDepartmentMapping(mappingId);
      if (response.success) {
        showSuccess('Mapping deleted successfully');
        loadInitialData();
      } else {
        showError(response.error || 'Failed to delete mapping');
      }
    } catch (error) {
      console.error('Failed to delete mapping:', error);
      showError('Failed to delete mapping');
    } finally {
      setLoading(false);
    }
  };

  const getProviderName = (providerId: string) => {
    const provider = providers.find(p => p.providerId === providerId);
    return provider ? `${provider.title} ${provider.firstName} ${provider.lastName}` : providerId;
  };

  const getDepartmentName = (departmentId: string) => {
    const department = departments.find(d => d.departmentId === departmentId);
    return department ? department.name : departmentId;
  };

  const getDepartmentCode = (departmentId: string) => {
    const department = departments.find(d => d.departmentId === departmentId);
    return department ? department.code : '';
  };

  const filteredMappings = mappings.filter(mapping => {
    const provider = providers.find(p => p.providerId === mapping.providerId);
    const department = departments.find(d => d.departmentId === mapping.departmentId);
    
    const matchesSearch = !searchTerm || 
      (provider && `${provider.firstName} ${provider.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (department && department.name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesDepartment = !selectedDepartment || mapping.departmentId === selectedDepartment;
    
    return matchesSearch && matchesDepartment;
  });

  const groupedMappings = filteredMappings.reduce((acc, mapping) => {
    const deptId = mapping.departmentId;
    if (!acc[deptId]) {
      acc[deptId] = [];
    }
    acc[deptId].push(mapping);
    return acc;
  }, {} as Record<string, ProviderDepartmentMapping[]>);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Provider-Department Mapping</h1>
          <p className="text-gray-600">Assign providers to departments and manage their roles</p>
        </div>
        <Button
          onClick={() => {
            resetForm();
            setShowForm(true);
          }}
          className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <Plus size={16} />
          <span>New Mapping</span>
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <Input
                type="text"
                placeholder="Search providers or departments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-48"
            >
              <option value="">All Departments</option>
              {departments.map(dept => (
                <option key={dept.departmentId} value={dept.departmentId}>
                  {dept.name} ({dept.code})
                </option>
              ))}
            </Select>
            
            <Button
              onClick={loadInitialData}
              disabled={loading}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>

      {/* Mappings by Department */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading mappings...</p>
        </div>
      ) : Object.keys(groupedMappings).length === 0 ? (
        <div className="text-center py-12">
          <UserCheck className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No Mappings Found</h3>
          <p className="text-gray-500">Create your first provider-department mapping to get started.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedMappings).map(([departmentId, deptMappings]) => {
            const department = departments.find(d => d.departmentId === departmentId);
            
            return (
              <div key={departmentId} className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Department Header */}
                <div className="bg-indigo-50 border-b border-gray-200 p-4">
                  <div className="flex items-center space-x-3">
                    <Building2 className="text-indigo-600" size={20} />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        {department?.name || 'Unknown Department'}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {department?.code} • {deptMappings.length} provider{deptMappings.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Providers List */}
                <div className="divide-y divide-gray-200">
                  {deptMappings.map((mapping) => {
                    const provider = providers.find(p => p.providerId === mapping.providerId);
                    
                    return (
                      <div key={mapping.mappingId} className="p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Users className="text-gray-400" size={16} />
                            <div>
                              <div className="font-medium text-gray-900">
                                {getProviderName(mapping.providerId)}
                              </div>
                              <div className="text-sm text-gray-600">
                                {provider?.specialization} • {provider?.qualification}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="flex items-center space-x-2">
                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                  mapping.isPrimary 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {mapping.role}
                                </span>
                                {mapping.isPrimary && (
                                  <span className="inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                    Primary
                                  </span>
                                )}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                Since {new Date(mapping.effectiveFrom).toLocaleDateString()}
                              </div>
                            </div>
                            
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => handleEditMapping(mapping)}
                                className="text-indigo-600 hover:text-indigo-800"
                              >
                                <Edit size={14} />
                              </Button>
                              <Button
                                onClick={() => handleDeleteMapping(mapping.mappingId)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Trash2 size={14} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Create Mapping Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 blur-backdrop flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">
                  {editingMapping ? 'Edit Provider-Department Mapping' : 'Create Provider-Department Mapping'}
                </h2>
                <Button
                  onClick={() => setShowForm(false)}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  ×
                </Button>
              </div>
            </div>

            <form onSubmit={handleCreateMapping} className="p-6 space-y-4">
              <FormField label="Provider" required>
                <Select
                  value={formData.providerId}
                  onChange={(e) => setFormData(prev => ({ ...prev, providerId: e.target.value }))}
                  required
                  disabled={!!editingMapping}
                >
                  <option value="">Select Provider</option>
                  {providers.map((provider) => (
                    <option key={provider.providerId} value={provider.providerId}>
                      {provider.title} {provider.firstName} {provider.lastName} - {provider.specialization}
                    </option>
                  ))}
                </Select>
                {editingMapping && (
                  <p className="text-xs text-gray-500 mt-1">Provider cannot be changed when editing</p>
                )}
              </FormField>

              <FormField label="Department" required>
                <Select
                  value={formData.departmentId}
                  onChange={(e) => setFormData(prev => ({ ...prev, departmentId: e.target.value }))}
                  required
                  disabled={!!editingMapping}
                >
                  <option value="">Select Department</option>
                  {departments.map((department) => (
                    <option key={department.departmentId} value={department.departmentId}>
                      {department.name} ({department.code})
                    </option>
                  ))}
                </Select>
                {editingMapping && (
                  <p className="text-xs text-gray-500 mt-1">Department cannot be changed when editing</p>
                )}
              </FormField>

              <FormField label="Role" required>
                <Select
                  value={formData.role}
                  onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as DepartmentRole }))}
                  required
                >
                  {departmentRoleOptions.map((role) => (
                    <option key={role.value} value={role.value}>
                      {role.label}
                    </option>
                  ))}
                </Select>
              </FormField>

              <FormField label="Effective From" required>
                <Input
                  type="date"
                  value={formData.effectiveFrom}
                  onChange={(e) => setFormData(prev => ({ ...prev, effectiveFrom: e.target.value }))}
                  required
                />
              </FormField>

              <FormField label="">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.isPrimary}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPrimary: e.target.checked }))}
                    className="form-checkbox h-4 w-4 text-indigo-600"
                  />
                  <span className="text-sm text-gray-700">Primary Department</span>
                </label>
              </FormField>

              <div className="flex justify-end space-x-4 pt-4">
                <Button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
                >
                  {loading
                    ? (editingMapping ? 'Updating...' : 'Creating...')
                    : (editingMapping ? 'Update Mapping' : 'Create Mapping')
                  }
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};
