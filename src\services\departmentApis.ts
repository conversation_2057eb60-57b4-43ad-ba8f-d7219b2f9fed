import type {
  Department,
  DepartmentWithProviders,
  ProviderDepartmentMapping,
  DepartmentStats,
  DepartmentFilters,
  DepartmentQueue
} from '../types/department';
import { DepartmentRole, commonDepartments } from '../types/department';
import type { ProviderWithDepartment, ProviderDepartmentSummary } from '../types/provider';
import { ProviderSpecialization, ProviderStatus } from '../types/appointmentenums';
import apiConfig, { getApiUrl, apiRequest } from '../config/apiConfig';

// Use configuration for mock data flag
const USE_MOCK_DATA = apiConfig.USE_MOCK_DATA;

// Helper function to check if error is CORS-related
const isCorsError = (error: Error): boolean => {
  return error.message.includes('CORS') ||
         error.message.includes('Failed to fetch') ||
         error.message.includes('Network request failed');
};

// Helper function to handle API calls with CORS fallback
const apiCallWithFallback = async <T>(
  apiCall: () => Promise<T>,
  mockCall: () => Promise<T>
): Promise<T> => {
  if (USE_MOCK_DATA) {
    return mockCall();
  }

  try {
    return await apiCall();
  } catch (error) {
    if (apiConfig.AUTO_FALLBACK_TO_MOCK && error instanceof Error && isCorsError(error)) {
      console.warn('API call failed due to CORS, falling back to mock data:', error.message);
      return mockCall();
    }
    throw error;
  }
};

// Mock Departments Data
const mockDepartments: Department[] = [
  {
    departmentId: "dept-001",
    facilityId: "fac-001",
    name: "Cardiology",
    code: "CARD",
    description: "Heart and cardiovascular diseases",
    headOfDepartment: "prov-001",
    phoneNumber: "+91-11-2345-6789",
    email: "<EMAIL>",
    location: "2nd Floor, Block A",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "08:00:00", endTime: "14:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Cardiac Consultation", "ECG", "Echocardiography", "Stress Testing", "Cardiac Catheterization"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-002",
    facilityId: "fac-001",
    name: "Orthopedics",
    code: "ORTHO",
    description: "Bone and joint disorders",
    headOfDepartment: "prov-002",
    phoneNumber: "+91-11-2345-6790",
    email: "<EMAIL>",
    location: "3rd Floor, Block B",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "09:00:00", endTime: "13:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Orthopedic Consultation", "X-Ray", "Joint Replacement", "Fracture Treatment", "Sports Medicine"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-003",
    facilityId: "fac-001",
    name: "Emergency",
    code: "EMER",
    description: "Emergency and trauma care",
    headOfDepartment: "prov-003",
    phoneNumber: "+91-11-2345-6791",
    email: "<EMAIL>",
    location: "Ground Floor, Block C",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "SUNDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true }
    ],
    services: ["Emergency Consultation", "Trauma Care", "Critical Care", "Ambulance Services", "Emergency Surgery"],
    isActive: true,
    isEmergencyDepartment: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-004",
    facilityId: "fac-001",
    name: "Pediatrics",
    code: "PEDIA",
    description: "Children's healthcare",
    headOfDepartment: "prov-004",
    phoneNumber: "+91-11-2345-6792",
    email: "<EMAIL>",
    location: "1st Floor, Block A",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "08:00:00", endTime: "14:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Pediatric Consultation", "Vaccination", "Growth Monitoring", "Pediatric Surgery", "Neonatal Care"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-005",
    facilityId: "fac-001",
    name: "Gynecology",
    code: "GYNE",
    description: "Women's reproductive health",
    headOfDepartment: "prov-005",
    phoneNumber: "+91-11-2345-6793",
    email: "<EMAIL>",
    location: "2nd Floor, Block B",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "09:00:00", endTime: "13:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Gynecological Consultation", "Prenatal Care", "Delivery Services", "Family Planning", "Gynecological Surgery"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  }
];

// Mock Provider-Department Mappings
const mockProviderDepartmentMappings: ProviderDepartmentMapping[] = [
  {
    mappingId: "map-001",
    providerId: "prov-001",
    departmentId: "dept-001",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-002",
    providerId: "prov-002",
    departmentId: "dept-002",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-003",
    providerId: "prov-003",
    departmentId: "dept-003",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-004",
    providerId: "prov-004",
    departmentId: "dept-004",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-005",
    providerId: "prov-005",
    departmentId: "dept-005",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  }
];



// API Functions

// Get all departments
export const getDepartments = async (filters?: DepartmentFilters) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));

    let filteredDepartments = [...mockDepartments];

    if (filters?.facilityId) {
      filteredDepartments = filteredDepartments.filter(dept => dept.facilityId === filters.facilityId);
    }

    if (filters?.isActive !== undefined) {
      filteredDepartments = filteredDepartments.filter(dept => dept.isActive === filters.isActive);
    }

    if (filters?.isEmergencyDepartment !== undefined) {
      filteredDepartments = filteredDepartments.filter(dept => dept.isEmergencyDepartment === filters.isEmergencyDepartment);
    }

    if (filters?.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filteredDepartments = filteredDepartments.filter(dept =>
        dept.name.toLowerCase().includes(searchTerm) ||
        dept.code.toLowerCase().includes(searchTerm) ||
        dept.description?.toLowerCase().includes(searchTerm)
      );
    }

    return {
      success: true,
      data: {
        results: filteredDepartments,
        totalElements: filteredDepartments.length,
        totalPages: Math.ceil(filteredDepartments.length / (filters?.size || 20)),
        currentPage: filters?.page || 0
      }
    };
  }

  // Real API implementation
  try {
    const queryParams = new URLSearchParams();

    if (filters?.facilityId) queryParams.append('facilityId', filters.facilityId);
    if (filters?.isActive !== undefined) queryParams.append('isActive', filters.isActive.toString());
    if (filters?.isEmergencyDepartment !== undefined) queryParams.append('isEmergencyDepartment', filters.isEmergencyDepartment.toString());
    if (filters?.searchTerm) queryParams.append('searchTerm', filters.searchTerm);
    if (filters?.page !== undefined) queryParams.append('page', filters.page.toString());
    if (filters?.size !== undefined) queryParams.append('size', filters.size.toString());

    const endpoint = `${apiConfig.ENDPOINTS.DEPARTMENTS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const url = getApiUrl(endpoint);

    const data = await apiRequest(url, { method: 'GET' });

    return {
      success: true,
      data: {
        results: data.content || data.data || data,
        totalElements: data.totalElements || data.total || (data.content ? data.content.length : 0),
        totalPages: data.totalPages || Math.ceil((data.totalElements || 0) / (filters?.size || apiConfig.PAGINATION.DEFAULT_SIZE)),
        currentPage: data.number || data.page || 0
      }
    };
  } catch (error) {
    console.error('Failed to fetch departments:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch departments'
    };
  }
};

// Get department by ID
export const getDepartmentById = async (departmentId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 200));

    const department = mockDepartments.find(dept => dept.departmentId === departmentId);

    if (!department) {
      return { success: false, error: "Department not found" };
    }

    return { success: true, data: department };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.DEPARTMENT_BY_ID(departmentId));
    const data = await apiRequest(url, { method: 'GET' });

    return { success: true, data };
  } catch (error) {
    console.error('Failed to fetch department:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch department'
    };
  }
};

// Get provider-department mappings
export const getProviderDepartmentMappings = async (filters?: { providerId?: string; departmentId?: string; facilityId?: string }) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 200));

    let filteredMappings = [...mockProviderDepartmentMappings];

    if (filters?.providerId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.providerId === filters.providerId);
    }

    if (filters?.departmentId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.departmentId === filters.departmentId);
    }

    if (filters?.facilityId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.facilityId === filters.facilityId);
    }

    return { success: true, data: filteredMappings };
  }

  // Real API implementation
  try {
    const queryParams = new URLSearchParams();

    if (filters?.providerId) queryParams.append('providerId', filters.providerId);
    if (filters?.departmentId) queryParams.append('departmentId', filters.departmentId);
    if (filters?.facilityId) queryParams.append('facilityId', filters.facilityId);

    const endpoint = `${apiConfig.ENDPOINTS.PROVIDER_DEPARTMENT_MAPPINGS}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const url = getApiUrl(endpoint);

    const data = await apiRequest(url, { method: 'GET' });

    return { success: true, data: data.content || data.data || data };
  } catch (error) {
    console.error('Failed to fetch provider-department mappings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch mappings'
    };
  }
};

// Create department
export const createDepartment = async (departmentData: Omit<Department, 'departmentId' | 'createdAt' | 'updatedAt'>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const newDepartment: Department = {
      ...departmentData,
      departmentId: `dept-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockDepartments.push(newDepartment);

    return { success: true, data: newDepartment };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.DEPARTMENTS);
    const data = await apiRequest(url, {
      method: 'POST',
      body: JSON.stringify(departmentData),
    });

    return { success: true, data };
  } catch (error) {
    console.error('Failed to create department:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create department'
    };
  }
};

// Update department
export const updateDepartment = async (departmentId: string, updates: Partial<Department>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockDepartments.findIndex(dept => dept.departmentId === departmentId);

    if (index === -1) {
      return { success: false, error: "Department not found" };
    }

    mockDepartments[index] = {
      ...mockDepartments[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    return { success: true, data: mockDepartments[index] };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.DEPARTMENT_BY_ID(departmentId));
    const data = await apiRequest(url, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });

    return { success: true, data };
  } catch (error) {
    console.error('Failed to update department:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update department'
    };
  }
};

// Create provider-department mapping
export const createProviderDepartmentMapping = async (mappingData: Omit<ProviderDepartmentMapping, 'mappingId' | 'createdAt' | 'updatedAt'>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const newMapping: ProviderDepartmentMapping = {
      ...mappingData,
      mappingId: `map-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockProviderDepartmentMappings.push(newMapping);

    return { success: true, data: newMapping };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.PROVIDER_DEPARTMENT_MAPPINGS);
    const data = await apiRequest(url, {
      method: 'POST',
      body: JSON.stringify(mappingData),
    });

    return { success: true, data };
  } catch (error) {
    console.error('Failed to create provider-department mapping:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create mapping'
    };
  }
};

// Delete department
export const deleteDepartment = async (departmentId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockDepartments.findIndex(dept => dept.departmentId === departmentId);

    if (index === -1) {
      return { success: false, error: "Department not found" };
    }

    mockDepartments.splice(index, 1);

    return { success: true, message: "Department deleted successfully" };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.DEPARTMENT_BY_ID(departmentId));
    await apiRequest(url, { method: 'DELETE' });

    return { success: true, message: "Department deleted successfully" };
  } catch (error) {
    console.error('Failed to delete department:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete department'
    };
  }
};

// Update provider-department mapping
export const updateProviderDepartmentMapping = async (mappingId: string, updates: Partial<ProviderDepartmentMapping>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockProviderDepartmentMappings.findIndex(mapping => mapping.mappingId === mappingId);

    if (index === -1) {
      return { success: false, error: "Mapping not found" };
    }

    mockProviderDepartmentMappings[index] = {
      ...mockProviderDepartmentMappings[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    return { success: true, data: mockProviderDepartmentMappings[index] };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.PROVIDER_DEPARTMENT_MAPPING_BY_ID(mappingId));
    const data = await apiRequest(url, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });

    return { success: true, data };
  } catch (error) {
    console.error('Failed to update provider-department mapping:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update mapping'
    };
  }
};

// Delete provider-department mapping
export const deleteProviderDepartmentMapping = async (mappingId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockProviderDepartmentMappings.findIndex(mapping => mapping.mappingId === mappingId);

    if (index === -1) {
      return { success: false, error: "Mapping not found" };
    }

    mockProviderDepartmentMappings.splice(index, 1);

    return { success: true, message: "Mapping deleted successfully" };
  }

  // Real API implementation
  try {
    const url = getApiUrl(apiConfig.ENDPOINTS.PROVIDER_DEPARTMENT_MAPPING_BY_ID(mappingId));
    await apiRequest(url, { method: 'DELETE' });

    return { success: true, message: "Mapping deleted successfully" };
  } catch (error) {
    console.error('Failed to delete provider-department mapping:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete mapping'
    };
  }
};

// Get department statistics
export const getDepartmentStats = async (departmentId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const department = mockDepartments.find(dept => dept.departmentId === departmentId);

    if (!department) {
      return { success: false, error: "Department not found" };
    }

    // Generate mock statistics
    const stats: DepartmentStats = {
      departmentId,
      departmentName: department.name,
      totalProviders: Math.floor(Math.random() * 10) + 3,
      activeProviders: Math.floor(Math.random() * 8) + 2,
      availableProviders: Math.floor(Math.random() * 5) + 1,
      appointmentsToday: Math.floor(Math.random() * 50) + 10,
      appointmentsThisWeek: Math.floor(Math.random() * 300) + 50,
      appointmentsThisMonth: Math.floor(Math.random() * 1200) + 200,
      currentQueueLength: Math.floor(Math.random() * 15) + 2,
      averageWaitTime: Math.floor(Math.random() * 30) + 10,
      utilizationRate: Math.floor(Math.random() * 40) + 60,
      peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
      lastUpdated: new Date().toISOString()
    };

    return { success: true, data: stats };
  }

  throw new Error("Real API not implemented yet");
};

// Get department queues with provider information
export const getDepartmentQueues = async (facilityId: string, date?: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 400));

    // Mock provider names for better display
    const mockProviderNames = [
      { id: "prov-001", name: "Dr. Sarah Johnson", title: "Dr.", specialization: "Cardiology" },
      { id: "prov-002", name: "Dr. Michael Chen", title: "Dr.", specialization: "Orthopedics" },
      { id: "prov-003", name: "Dr. Emily Rodriguez", title: "Dr.", specialization: "Emergency Medicine" },
      { id: "prov-004", name: "Dr. David Kumar", title: "Dr.", specialization: "Pediatrics" },
      { id: "prov-005", name: "Dr. Lisa Thompson", title: "Dr.", specialization: "Gynecology" },
      { id: "prov-006", name: "Dr. James Wilson", title: "Dr.", specialization: "Cardiology" },
      { id: "prov-007", name: "Dr. Maria Garcia", title: "Dr.", specialization: "Orthopedics" },
      { id: "prov-008", name: "Dr. Robert Lee", title: "Dr.", specialization: "Emergency Medicine" },
      { id: "prov-009", name: "Dr. Jennifer Brown", title: "Dr.", specialization: "Pediatrics" },
      { id: "prov-010", name: "Dr. Ahmed Hassan", title: "Dr.", specialization: "Gynecology" }
    ];

    const departmentQueues: DepartmentQueue[] = mockDepartments.map(department => {
      const providerMappings = mockProviderDepartmentMappings.filter(
        mapping => mapping.departmentId === department.departmentId && mapping.isActive
      );

      // Add additional providers for better display (2-4 providers per department)
      const additionalProviders = Math.floor(Math.random() * 3) + 1;
      const allProviders = [...providerMappings];

      for (let i = 0; i < additionalProviders; i++) {
        const randomProvider = mockProviderNames[Math.floor(Math.random() * mockProviderNames.length)];
        allProviders.push({
          mappingId: `map-${Date.now()}-${i}`,
          providerId: randomProvider.id,
          departmentId: department.departmentId,
          facilityId,
          role: DepartmentRole.Consultant,
          isPrimary: false,
          effectiveFrom: new Date().toISOString().split('T')[0],
          isActive: true,
          createdAt: new Date().toISOString()
        });
      }

      const providerQueues = allProviders.map(mapping => {
        const providerInfo = mockProviderNames.find(p => p.id === mapping.providerId) ||
          { id: mapping.providerId, name: `Dr. Provider ${mapping.providerId.slice(-1)}`, title: "Dr.", specialization: "General Medicine" };

        const isAvailable = Math.random() > 0.2; // 80% chance of being available
        const hasQueue = isAvailable && Math.random() > 0.3; // 70% chance of having queue if available
        const queueLength = hasQueue ? Math.floor(Math.random() * 8) + 1 : 0;

        return {
          providerId: mapping.providerId,
          providerName: providerInfo.name,
          providerTitle: providerInfo.title,
          specialization: providerInfo.specialization,
          queueLength,
          currentPatient: hasQueue && Math.random() > 0.5 ? {
            patientName: `Patient ${Math.floor(Math.random() * 100) + 1}`,
            queueNumber: 1,
            serviceStartTime: new Date().toISOString()
          } : undefined,
          nextPatients: hasQueue ? Array.from({ length: Math.min(queueLength - 1, 3) }, (_, i) => ({
            patientName: `Patient ${Math.floor(Math.random() * 100) + 1}`,
            queueNumber: i + 2,
            estimatedTime: new Date(Date.now() + (i + 1) * 15 * 60 * 1000).toISOString()
          })) : [],
          isAvailable,
          status: !isAvailable ? "OFFLINE" : hasQueue && Math.random() > 0.7 ? "BUSY" : "AVAILABLE",
          averageServiceTime: 15 + Math.floor(Math.random() * 10),
          estimatedWaitTime: queueLength * (15 + Math.floor(Math.random() * 10))
        };
      });

      const totalInQueue = providerQueues.reduce((sum, pq) => sum + pq.queueLength, 0);
      const currentlyServingProvider = providerQueues.find(pq => pq.currentPatient);

      return {
        departmentId: department.departmentId,
        departmentName: department.name,
        departmentCode: department.code,
        totalInQueue,
        currentlyServing: currentlyServingProvider ? {
          providerId: currentlyServingProvider.providerId,
          providerName: currentlyServingProvider.providerName,
          patientName: currentlyServingProvider.currentPatient!.patientName,
          queueNumber: currentlyServingProvider.currentPatient!.queueNumber,
          serviceStartTime: currentlyServingProvider.currentPatient!.serviceStartTime
        } : undefined,
        providerQueues,
        averageWaitTime: Math.floor(Math.random() * 20) + 10,
        estimatedWaitTime: Math.floor(Math.random() * 30) + 15,
        peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
        lastUpdated: new Date().toISOString()
      };
    });

    return { success: true, data: departmentQueues };
  }

  throw new Error("Real API not implemented yet");
};

// Get today's available doctors by department
export const getTodayAvailableDoctors = async (facilityId: string, date?: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const today = date || new Date().toISOString().split('T')[0];
    const currentTime = new Date();
    const currentHour = currentTime.getHours();

    // Mock available doctors for today
    const availableDoctors = mockDepartments.map(department => {
      const doctorsCount = Math.floor(Math.random() * 4) + 2; // 2-5 doctors per department
      const doctors = Array.from({ length: doctorsCount }, (_, i) => {
        const isCurrentlyAvailable = currentHour >= 9 && currentHour <= 17 && Math.random() > 0.2;
        const hasSchedule = Math.random() > 0.1; // 90% have schedule for today

        return {
          providerId: `prov-${department.departmentId}-${i + 1}`,
          name: `Dr. ${['Sarah', 'Michael', 'Emily', 'David', 'Lisa', 'James', 'Maria', 'Robert'][i] || 'Provider'} ${['Johnson', 'Chen', 'Rodriguez', 'Kumar', 'Thompson'][Math.floor(Math.random() * 5)]}`,
          title: "Dr.",
          specialization: department.name,
          isAvailable: isCurrentlyAvailable,
          hasScheduleToday: hasSchedule,
          scheduleTime: hasSchedule ? `${9 + Math.floor(Math.random() * 8)}:00 - ${15 + Math.floor(Math.random() * 3)}:00` : null,
          currentStatus: !hasSchedule ? "No Schedule" : !isCurrentlyAvailable ? "Offline" : Math.random() > 0.7 ? "Busy" : "Available",
          nextAvailableTime: !isCurrentlyAvailable && hasSchedule ? `${currentHour + 1}:00` : null,
          patientsToday: Math.floor(Math.random() * 20) + 5,
          currentQueueLength: isCurrentlyAvailable ? Math.floor(Math.random() * 8) : 0
        };
      });

      return {
        departmentId: department.departmentId,
        departmentName: department.name,
        departmentCode: department.code,
        totalDoctors: doctors.length,
        availableDoctors: doctors.filter(d => d.isAvailable).length,
        doctors
      };
    });

    return { success: true, data: availableDoctors };
  }

  throw new Error("Real API not implemented yet");
};
