import type {
  Department,
  DepartmentWithProviders,
  ProviderDepartmentMapping,
  DepartmentStats,
  DepartmentFilters,
  DepartmentQueue
} from '../types/department';
import { DepartmentRole, commonDepartments } from '../types/department';
import type { ProviderWithDepartment, ProviderDepartmentSummary } from '../types/provider';
import { ProviderSpecialization, ProviderStatus } from '../types/appointmentenums';

// Mock data flag
const USE_MOCK_DATA = true;

// Mock Departments Data
const mockDepartments: Department[] = [
  {
    departmentId: "dept-001",
    facilityId: "fac-001",
    name: "Cardiology",
    code: "CARD",
    description: "Heart and cardiovascular diseases",
    headOfDepartment: "prov-001",
    phoneNumber: "+91-11-2345-6789",
    email: "<EMAIL>",
    location: "2nd Floor, Block A",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "08:00:00", endTime: "14:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Cardiac Consultation", "ECG", "Echocardiography", "Stress Testing", "Cardiac Catheterization"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-002",
    facilityId: "fac-001",
    name: "Orthopedics",
    code: "ORTHO",
    description: "Bone and joint disorders",
    headOfDepartment: "prov-002",
    phoneNumber: "+91-11-2345-6790",
    email: "<EMAIL>",
    location: "3rd Floor, Block B",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "09:00:00", endTime: "13:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Orthopedic Consultation", "X-Ray", "Joint Replacement", "Fracture Treatment", "Sports Medicine"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-003",
    facilityId: "fac-001",
    name: "Emergency",
    code: "EMER",
    description: "Emergency and trauma care",
    headOfDepartment: "prov-003",
    phoneNumber: "+91-11-2345-6791",
    email: "<EMAIL>",
    location: "Ground Floor, Block C",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true },
      { dayOfWeek: "SUNDAY", isOperating: true, startTime: "00:00:00", endTime: "23:59:59", emergencyHours: true }
    ],
    services: ["Emergency Consultation", "Trauma Care", "Critical Care", "Ambulance Services", "Emergency Surgery"],
    isActive: true,
    isEmergencyDepartment: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-004",
    facilityId: "fac-001",
    name: "Pediatrics",
    code: "PEDIA",
    description: "Children's healthcare",
    headOfDepartment: "prov-004",
    phoneNumber: "+91-11-2345-6792",
    email: "<EMAIL>",
    location: "1st Floor, Block A",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "08:00:00", endTime: "18:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "08:00:00", endTime: "14:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Pediatric Consultation", "Vaccination", "Growth Monitoring", "Pediatric Surgery", "Neonatal Care"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    departmentId: "dept-005",
    facilityId: "fac-001",
    name: "Gynecology",
    code: "GYNE",
    description: "Women's reproductive health",
    headOfDepartment: "prov-005",
    phoneNumber: "+91-11-2345-6793",
    email: "<EMAIL>",
    location: "2nd Floor, Block B",
    operatingHours: [
      { dayOfWeek: "MONDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "TUESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "WEDNESDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "THURSDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "FRIDAY", isOperating: true, startTime: "09:00:00", endTime: "17:00:00", breakStartTime: "13:00:00", breakEndTime: "14:00:00" },
      { dayOfWeek: "SATURDAY", isOperating: true, startTime: "09:00:00", endTime: "13:00:00" },
      { dayOfWeek: "SUNDAY", isOperating: false, startTime: "00:00:00", endTime: "00:00:00" }
    ],
    services: ["Gynecological Consultation", "Prenatal Care", "Delivery Services", "Family Planning", "Gynecological Surgery"],
    isActive: true,
    isEmergencyDepartment: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  }
];

// Mock Provider-Department Mappings
const mockProviderDepartmentMappings: ProviderDepartmentMapping[] = [
  {
    mappingId: "map-001",
    providerId: "prov-001",
    departmentId: "dept-001",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-002",
    providerId: "prov-002",
    departmentId: "dept-002",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-003",
    providerId: "prov-003",
    departmentId: "dept-003",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-004",
    providerId: "prov-004",
    departmentId: "dept-004",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  },
  {
    mappingId: "map-005",
    providerId: "prov-005",
    departmentId: "dept-005",
    facilityId: "fac-001",
    role: DepartmentRole.Head,
    isPrimary: true,
    effectiveFrom: "2024-01-01",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z"
  }
];

// API Functions

// Get all departments
export const getDepartments = async (filters?: DepartmentFilters) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    let filteredDepartments = [...mockDepartments];
    
    if (filters?.facilityId) {
      filteredDepartments = filteredDepartments.filter(dept => dept.facilityId === filters.facilityId);
    }
    
    if (filters?.isActive !== undefined) {
      filteredDepartments = filteredDepartments.filter(dept => dept.isActive === filters.isActive);
    }
    
    if (filters?.isEmergencyDepartment !== undefined) {
      filteredDepartments = filteredDepartments.filter(dept => dept.isEmergencyDepartment === filters.isEmergencyDepartment);
    }
    
    if (filters?.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filteredDepartments = filteredDepartments.filter(dept => 
        dept.name.toLowerCase().includes(searchTerm) ||
        dept.code.toLowerCase().includes(searchTerm) ||
        dept.description?.toLowerCase().includes(searchTerm)
      );
    }
    
    return {
      success: true,
      data: {
        results: filteredDepartments,
        totalElements: filteredDepartments.length,
        totalPages: Math.ceil(filteredDepartments.length / (filters?.size || 20)),
        currentPage: filters?.page || 0
      }
    };
  }
  
  // Real API implementation would go here
  throw new Error("Real API not implemented yet");
};

// Get department by ID
export const getDepartmentById = async (departmentId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const department = mockDepartments.find(dept => dept.departmentId === departmentId);
    
    if (!department) {
      return { success: false, error: "Department not found" };
    }
    
    return { success: true, data: department };
  }
  
  throw new Error("Real API not implemented yet");
};

// Get provider-department mappings
export const getProviderDepartmentMappings = async (filters?: { providerId?: string; departmentId?: string; facilityId?: string }) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    let filteredMappings = [...mockProviderDepartmentMappings];
    
    if (filters?.providerId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.providerId === filters.providerId);
    }
    
    if (filters?.departmentId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.departmentId === filters.departmentId);
    }
    
    if (filters?.facilityId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.facilityId === filters.facilityId);
    }
    
    return { success: true, data: filteredMappings };
  }
  
  throw new Error("Real API not implemented yet");
};

// Create department
export const createDepartment = async (departmentData: Omit<Department, 'departmentId' | 'createdAt' | 'updatedAt'>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newDepartment: Department = {
      ...departmentData,
      departmentId: `dept-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    mockDepartments.push(newDepartment);
    
    return { success: true, data: newDepartment };
  }
  
  throw new Error("Real API not implemented yet");
};

// Update department
export const updateDepartment = async (departmentId: string, updates: Partial<Department>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = mockDepartments.findIndex(dept => dept.departmentId === departmentId);
    
    if (index === -1) {
      return { success: false, error: "Department not found" };
    }
    
    mockDepartments[index] = {
      ...mockDepartments[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    return { success: true, data: mockDepartments[index] };
  }
  
  throw new Error("Real API not implemented yet");
};

// Create provider-department mapping
export const createProviderDepartmentMapping = async (mappingData: Omit<ProviderDepartmentMapping, 'mappingId' | 'createdAt' | 'updatedAt'>) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newMapping: ProviderDepartmentMapping = {
      ...mappingData,
      mappingId: `map-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    mockProviderDepartmentMappings.push(newMapping);
    
    return { success: true, data: newMapping };
  }
  
  throw new Error("Real API not implemented yet");
};

// Get department statistics
export const getDepartmentStats = async (departmentId: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const department = mockDepartments.find(dept => dept.departmentId === departmentId);

    if (!department) {
      return { success: false, error: "Department not found" };
    }

    // Generate mock statistics
    const stats: DepartmentStats = {
      departmentId,
      departmentName: department.name,
      totalProviders: Math.floor(Math.random() * 10) + 3,
      activeProviders: Math.floor(Math.random() * 8) + 2,
      availableProviders: Math.floor(Math.random() * 5) + 1,
      appointmentsToday: Math.floor(Math.random() * 50) + 10,
      appointmentsThisWeek: Math.floor(Math.random() * 300) + 50,
      appointmentsThisMonth: Math.floor(Math.random() * 1200) + 200,
      currentQueueLength: Math.floor(Math.random() * 15) + 2,
      averageWaitTime: Math.floor(Math.random() * 30) + 10,
      utilizationRate: Math.floor(Math.random() * 40) + 60,
      peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
      lastUpdated: new Date().toISOString()
    };

    return { success: true, data: stats };
  }

  throw new Error("Real API not implemented yet");
};

// Get department queues with provider information
export const getDepartmentQueues = async (facilityId: string, date?: string) => {
  if (USE_MOCK_DATA) {
    await new Promise(resolve => setTimeout(resolve, 400));

    const departmentQueues: DepartmentQueue[] = mockDepartments.map(department => {
      const providerMappings = mockProviderDepartmentMappings.filter(
        mapping => mapping.departmentId === department.departmentId && mapping.isActive
      );

      const providerQueues = providerMappings.map(mapping => ({
        providerId: mapping.providerId,
        providerName: `Dr. Provider ${mapping.providerId.slice(-1)}`,
        providerTitle: "Dr.",
        specialization: "General Medicine",
        queueLength: Math.floor(Math.random() * 8) + 1,
        currentPatient: Math.random() > 0.5 ? {
          patientName: "John Doe",
          queueNumber: 1,
          serviceStartTime: new Date().toISOString()
        } : undefined,
        nextPatients: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, i) => ({
          patientName: `Patient ${i + 2}`,
          queueNumber: i + 2,
          estimatedTime: new Date(Date.now() + (i + 1) * 15 * 60 * 1000).toISOString()
        })),
        isAvailable: Math.random() > 0.3,
        status: Math.random() > 0.7 ? "BUSY" : "AVAILABLE",
        averageServiceTime: 15 + Math.floor(Math.random() * 10),
        estimatedWaitTime: (Math.floor(Math.random() * 8) + 1) * 15
      }));

      const totalInQueue = providerQueues.reduce((sum, pq) => sum + pq.queueLength, 0);

      return {
        departmentId: department.departmentId,
        departmentName: department.name,
        departmentCode: department.code,
        totalInQueue,
        currentlyServing: providerQueues.find(pq => pq.currentPatient) ? {
          providerId: providerQueues.find(pq => pq.currentPatient)!.providerId,
          providerName: providerQueues.find(pq => pq.currentPatient)!.providerName,
          patientName: providerQueues.find(pq => pq.currentPatient)!.currentPatient!.patientName,
          queueNumber: providerQueues.find(pq => pq.currentPatient)!.currentPatient!.queueNumber,
          serviceStartTime: providerQueues.find(pq => pq.currentPatient)!.currentPatient!.serviceStartTime
        } : undefined,
        providerQueues,
        averageWaitTime: Math.floor(Math.random() * 20) + 10,
        estimatedWaitTime: Math.floor(Math.random() * 30) + 15,
        peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
        lastUpdated: new Date().toISOString()
      };
    });

    return { success: true, data: departmentQueues };
  }

  throw new Error("Real API not implemented yet");
};
