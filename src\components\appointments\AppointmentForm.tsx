import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { appointmentSchema, type AppointmentFormData, type AppointmentFormInput } from "../../zod_validations/appointment/appointmentSchema";
import { FormField } from "../../commonfields/FormField";
import { Input } from "../../commonfields/Input";
import { Select } from "../../commonfields/Select";
import { Calendar } from "../../commonfields/Calendar";
import { Button } from "../../commonfields/Button";
import FormMessage from "../../commonfields/FormMessage";
import FacilitySelector from "../../commonfields/FacilitySelector";
import { SlotSelectionPanel } from "../schedule/SlotSelectionPanel";
import {
  appointmentTypeOptions,
  appointmentPriorityOptions,
  slotDurationOptions,
  recurringPatternOptions,
  SlotDuration,
  AppointmentType,
  AppointmentPriority,
  RecurringPattern
} from "../../types/appointmentenums";
import { createAppointment, updateAppointment } from "../../services/appointmentApis";
import { getProviders } from "../../services/providerApis";
import { searchPatientsForDropdown } from "../../services/patientApis";
import { showError } from "../../utils/toastUtils";
import { useAppointmentStore } from "../../store/appointmentStore";
import type { Appointment } from "../../types/appointment";
import type { AvailableSlot } from "../../types/schedule";

interface AppointmentFormProps {
  appointmentId?: string;
  initialData?: Partial<Appointment>;
  onSuccess?: (appointment?: Appointment) => void;
  onCancel?: () => void;
  selectedDate?: string;
  selectedTime?: string;
  onProviderChange?: (providerId: string) => void;
  bookedSlots?: string[];
  isSlotBooked?: (time: string) => boolean;
  editingAppointment?: Appointment | null;
}

export const AppointmentForm: React.FC<AppointmentFormProps> = ({
  appointmentId,
  initialData,
  onSuccess,
  onCancel,
  selectedDate,
  selectedTime,
  onProviderChange,
  bookedSlots = [],
  isSlotBooked,
  editingAppointment
}) => {
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<any[]>([]);
  const [patients, setPatients] = useState<any[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(false);
  const [loadingPatients, setLoadingPatients] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<AvailableSlot | null>(null);
  const [showSlotPanel, setShowSlotPanel] = useState(false);

  const { addAppointment, updateAppointment: updateAppointmentInStore } = useAppointmentStore();
  const isEditMode = !!appointmentId || !!editingAppointment;
  const appointmentData = editingAppointment || initialData;

  // Helper function to extract date from datetime string
  const extractDateFromDateTime = (dateTime: string): string => {
    if (!dateTime) return "";
    // If it's already in YYYY-MM-DD format, return as is
    if (dateTime.match(/^\d{4}-\d{2}-\d{2}$/)) return dateTime;
    // If it's in datetime format, extract the date part
    return dateTime.split('T')[0];
  };

  // Helper function to extract time from datetime string
  const extractTimeFromDateTime = (dateTime: string): string => {
    if (!dateTime) return "";
    // If it contains 'T', extract time part
    if (dateTime.includes('T')) {
      const timePart = dateTime.split('T')[1];
      // Remove seconds if present
      return timePart.split(':').slice(0, 2).join(':');
    }
    // If it's already in HH:MM format, return as is
    if (dateTime.match(/^\d{2}:\d{2}$/)) return dateTime;
    return "";
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<AppointmentFormInput>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      patientId: appointmentData?.patientId || "",
      providerId: appointmentData?.providerId || "",
      facilityId: appointmentData?.facilityId || "",
      appointmentDate: selectedDate || extractDateFromDateTime(appointmentData?.appointmentDate || ""),
      startTime: selectedTime || extractTimeFromDateTime(appointmentData?.startTime || ""),
      endTime: extractTimeFromDateTime(appointmentData?.endTime || ""),
      duration: appointmentData?.duration?.toString() || SlotDuration.Thirty.toString(),
      type: appointmentData?.type || AppointmentType.Consultation,
      priority: appointmentData?.priority || AppointmentPriority.Normal,
      title: appointmentData?.title || "",
      description: appointmentData?.description || "",
      notes: appointmentData?.notes || "",
      reason: appointmentData?.reason || "",
      isRecurring: appointmentData?.isRecurring || false,
      recurringPattern: appointmentData?.recurringPattern || RecurringPattern.None,
      recurringEndDate: extractDateFromDateTime(appointmentData?.recurringEndDate || "")
    }
  });

  const watchIsRecurring = watch("isRecurring");
  const watchStartTime = watch("startTime");
  const watchDuration = watch("duration");

  // Load providers and patients on component mount
  useEffect(() => {
    loadProviders();
    loadPatients();
  }, []);

  // Update form when selectedDate or selectedTime changes
  useEffect(() => {
    if (selectedDate) {
      setValue("appointmentDate", selectedDate);
    }
    if (selectedTime) {
      setValue("startTime", selectedTime);
    }
  }, [selectedDate, selectedTime, setValue]);

  // Auto-calculate end time when start time or duration changes
  useEffect(() => {
    if (watchStartTime && watchDuration) {
      const [hours, minutes] = watchStartTime.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + Number(watchDuration);
      const endHours = Math.floor(endMinutes / 60);
      const endMins = endMinutes % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      setValue("endTime", endTime);
    }
  }, [watchStartTime, watchDuration, setValue]);

  // Show/hide slot panel based on provider and date selection
  useEffect(() => {
    const providerId = watch("providerId");
    const appointmentDate = watch("appointmentDate");

    if (providerId && appointmentDate) {
      setShowSlotPanel(true);
    } else {
      setShowSlotPanel(false);
      setSelectedSlot(null);
    }
  }, [watch("providerId"), watch("appointmentDate")]);

  const loadProviders = async () => {
    setLoadingProviders(true);
    try {
      const response = await getProviders({ isActive: true, size: 100 });
      setProviders(response.results || []);
    } catch (error) {
      console.error("Failed to load providers:", error);
      showError("Failed to load providers");
    } finally {
      setLoadingProviders(false);
    }
  };

  const loadPatients = async () => {
    setLoadingPatients(true);
    try {
      // Use the optimized search function for dropdown
      const response = await searchPatientsForDropdown();
      setPatients(response || []);

      if (response.length === 0) {
        console.warn("No patients found. This might indicate an API issue or empty database.");
      }
    } catch (error) {
      console.error("Failed to load patients:", error);
      showError("Failed to load patients. Please check your connection and try again.");
      setPatients([]); // Set empty array on error
    } finally {
      setLoadingPatients(false);
    }
  };

  const handleSlotSelect = (slot: AvailableSlot) => {
    setSelectedSlot(slot);

    // Auto-populate form fields from selected slot
    setValue("appointmentDate", slot.date);
    setValue("startTime", slot.startTime.substring(0, 5)); // Convert HH:MM:SS to HH:MM
    setValue("endTime", slot.endTime.substring(0, 5));
    setValue("duration", slot.duration.toString());
  };

  const handleProviderChange = (providerId: string) => {
    setValue("providerId", providerId);
    onProviderChange?.(providerId);

    // Show slot panel when provider is selected and date is available
    if (providerId && watch("appointmentDate")) {
      setShowSlotPanel(true);
    } else {
      setShowSlotPanel(false);
      setSelectedSlot(null);
    }
  };

  const onSubmit = async (data: AppointmentFormInput) => {
    setLoading(true);
    try {
      // Transform all date/time fields to ISO datetime format
      const transformedData = {
        ...data,
        // Convert appointmentDate to datetime format
        appointmentDate: `${data.appointmentDate}T${data.startTime}:00`,
        // Convert startTime to datetime format
        startTime: `${data.appointmentDate}T${data.startTime}:00`,
        // Convert endTime to datetime format
        endTime: `${data.appointmentDate}T${data.endTime}:00`,
        // Convert recurringEndDate to datetime format if present
        recurringEndDate: data.recurringEndDate ? `${data.recurringEndDate}T23:59:59` : data.recurringEndDate,
        // Ensure proper type casting
        duration: typeof data.duration === 'string' ? parseInt(data.duration) as SlotDuration : data.duration,
        type: data.type as AppointmentType,
        priority: data.priority as AppointmentPriority
      };

      if (isEditMode && (appointmentId || editingAppointment?.appointmentId)) {
        const idToUpdate = appointmentId || editingAppointment?.appointmentId!;
        const result = await updateAppointment(idToUpdate, transformedData as any);
        if (result.success) {
          updateAppointmentInStore(idToUpdate, result.data);
          // Success message will be shown by the parent component
          onSuccess?.(result.data);
        } else {
          showError(result.error || "Failed to update appointment");
        }
      } else {
        const result = await createAppointment(transformedData as any);
        if (result.success) {
          addAppointment(result.data);
          // Success message will be shown by the parent component
          reset();
          onSuccess?.(result.data);
        } else {
          showError(result.error || "Failed to create appointment");
        }
      }
    } catch (error) {
      console.error("Appointment form error:", error);
      showError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Patient Details Section */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3">
            <span className="text-purple-600 text-sm font-medium">👤</span>
          </div>
          Patient Details
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField label="Patient" required>
            <Select {...register("patientId")} disabled={loadingPatients} className="rounded-lg">
              <option value="">
                {loadingPatients ? "Loading patients..." : "Select Patient"}
              </option>
              {patients.map((patient) => {
                // Create a display name with fallbacks
                const firstName = patient.firstName || "";
                const lastName = patient.lastName || "";
                const fullName = `${firstName} ${lastName}`.trim() || "Unnamed Patient";
                const identifier = patient.identifierNumber || patient.upId || patient.patientId || "";
                const mobile = patient.contacts?.[0]?.mobileNumber || "";

                // Create a comprehensive display string
                let displayText = fullName;
                if (identifier) displayText += ` (${identifier})`;
                if (mobile) displayText += ` - ${mobile}`;

                return (
                  <option key={patient.patientId} value={patient.patientId}>
                    {displayText}
                  </option>
                );
              })}
            </Select>
            <FormMessage>{errors.patientId?.message}</FormMessage>
            {patients.length === 0 && !loadingPatients && (
              <p className="text-sm text-gray-500 mt-1">
                No patients found. Please ensure patients are registered in the system.
              </p>
            )}
          </FormField>

          <FormField label="Phone Number">
            <Input
              value={patients.find(p => p.patientId === watch("patientId"))?.contacts?.[0]?.mobileNumber || ""}
              readOnly
              className="bg-gray-50 rounded-lg"
              placeholder="Phone number will appear here"
            />
          </FormField>
        </div>
      </div>

      {/* Practitioner Details Section */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3">
            <span className="text-purple-600 text-sm font-medium">👨‍⚕️</span>
          </div>
          Practitioner Details
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField label="Department" required>
            <Select className="rounded-lg">
              <option value="">All Departments</option>
              <option value="orthopedics">Orthopedics</option>
              <option value="cardiology">Cardiology</option>
              <option value="neurology">Neurology</option>
              <option value="pediatrics">Pediatrics</option>
            </Select>
          </FormField>

          <FormField label="Practitioner" required>
            <Select
              {...register("providerId")}
              disabled={loadingProviders}
              onChange={(e) => handleProviderChange(e.target.value)}
              className="rounded-lg"
            >
              <option value="">Select Practitioner</option>
              {providers.map((provider) => (
                <option key={provider.providerId} value={provider.providerId}>
                  {provider.title} {provider.firstName} {provider.lastName}
                </option>
              ))}
            </Select>
            <FormMessage>{errors.providerId?.message}</FormMessage>
          </FormField>
        </div>
      </div>

      {/* Appointment Details Section */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3">
            <span className="text-purple-600 text-sm font-medium">📅</span>
          </div>
          Appointment Details
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField label="Date" required>
            <Calendar {...register("appointmentDate")} className="rounded-lg" />
            <FormMessage>{errors.appointmentDate?.message}</FormMessage>
          </FormField>

          <FormField label="Time" required>
            <Input
              {...register("startTime")}
              type="time"
              className={`rounded-lg ${isSlotBooked?.(watch("startTime")) ? "border-red-500" : ""}`}
              onChange={(e) => {
                setValue("startTime", e.target.value);
                if (isSlotBooked?.(e.target.value)) {
                  showError("This time slot is already booked for the selected provider");
                }
              }}
            />
            <FormMessage>{errors.startTime?.message}</FormMessage>
            {isSlotBooked?.(watch("startTime")) && (
              <p className="text-sm text-red-600 mt-1">
                This time slot is already booked for the selected provider
              </p>
            )}
          </FormField>

          <FormField label="Duration" required>
            <Select {...register("duration")} className="rounded-lg">
              <option value="">Select Duration</option>
              {slotDurationOptions.map((duration) => (
                <option key={duration} value={duration}>
                  {duration} minutes
                </option>
              ))}
            </Select>
            <FormMessage>{errors.duration?.message as string}</FormMessage>
          </FormField>

          <FormField label="End Time">
            <Input
              {...register("endTime")}
              type="time"
              readOnly
              className="bg-gray-50 rounded-lg"
            />
            <FormMessage>{errors.endTime?.message}</FormMessage>
          </FormField>

          <FormField label="Visit Type" required>
            <Select {...register("type")} className="rounded-lg">
              {appointmentTypeOptions.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </Select>
            <FormMessage>{errors.type?.message}</FormMessage>
          </FormField>

          <FormField label="Priority" required>
            <Select {...register("priority")} className="rounded-lg">
              {appointmentPriorityOptions.map((priority) => (
                <option key={priority} value={priority}>
                  {priority}
                </option>
              ))}
            </Select>
            <FormMessage>{errors.priority?.message}</FormMessage>
          </FormField>
        </div>

        {/* Facility Selection */}
        <div className="mt-4">
          <FormField label="Facility" required>
            <FacilitySelector
              name="facilityId"
              value={watch("facilityId")}
              onChange={(e) => setValue("facilityId", e.target.value)}
              placeholder="Select Facility"
              className="rounded-lg"
            />
            <FormMessage>{errors.facilityId?.message}</FormMessage>
          </FormField>
        </div>
      </div>

      {/* Slot Selection Panel */}
      {showSlotPanel && (
        <div className="mt-6">
          <SlotSelectionPanel
            providerId={watch("providerId")}
            selectedDate={watch("appointmentDate")}
            onSlotSelect={handleSlotSelect}
            selectedSlot={selectedSlot}
            className="mb-6"
          />
        </div>
      )}

      {/* Additional Information */}
      <div className="space-y-4">
        <FormField label="Title">
          <Input {...register("title")} placeholder="Appointment title" />
          <FormMessage>{errors.title?.message}</FormMessage>
        </FormField>

        <FormField label="Description">
          <textarea
            {...register("description")}
            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
            rows={3}
            placeholder="Appointment description"
          />
          <FormMessage>{errors.description?.message}</FormMessage>
        </FormField>

        <FormField label="Reason">
          <Input {...register("reason")} placeholder="Reason for appointment" />
          <FormMessage>{errors.reason?.message}</FormMessage>
        </FormField>

        <FormField label="Notes">
          <textarea
            {...register("notes")}
            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
            rows={2}
            placeholder="Additional notes"
          />
          <FormMessage>{errors.notes?.message}</FormMessage>
        </FormField>
      </div>

      {/* Recurring Appointment Options */}
      <div className="space-y-4">
        <FormField label="">
          <label className="flex items-center space-x-2">
            <input
              {...register("isRecurring")}
              type="checkbox"
              className="form-checkbox h-4 w-4 text-indigo-600"
            />
            <span className="text-sm text-gray-700">Recurring Appointment</span>
          </label>
        </FormField>

        {watchIsRecurring && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Recurring Pattern" required>
              <Select {...register("recurringPattern")}>
                {recurringPatternOptions.map((pattern) => (
                  <option key={pattern} value={pattern}>
                    {pattern}
                  </option>
                ))}
              </Select>
              <FormMessage>{errors.recurringPattern?.message}</FormMessage>
            </FormField>

            <FormField label="End Date" required>
              <Calendar {...register("recurringEndDate")} />
              <FormMessage>{errors.recurringEndDate?.message}</FormMessage>
            </FormField>
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
        {onCancel && (
          <Button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium"
          >
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          disabled={loading}
          className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
        >
          {loading ? "Saving..." : isEditMode ? "Update Appointment" : "Book Appointment"}
        </Button>
      </div>
    </form>
  );
};



