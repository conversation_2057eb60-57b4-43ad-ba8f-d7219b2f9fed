import { create } from "zustand";
import type {
  QueueEntry,
  QueueSummary,
  QueueStatistics,
  WaitTimeEstimation,
  ServiceType
} from "../types/queue";
import { QueueStatus } from "../types/queue";

interface QueueState {
  // Queue data
  queues: { [key in ServiceType]?: QueueSummary };
  currentQueue: QueueSummary | null;
  queueStatistics: QueueStatistics | null;
  waitTimeEstimations: { [key in ServiceType]?: WaitTimeEstimation };
  
  // Selected service and facility
  selectedServiceType: ServiceType | null;
  selectedFacilityId: string | null;
  
  // Loading states
  loading: boolean;
  updating: boolean;
  
  // Real-time updates
  lastUpdated: string | null;
  autoRefresh: boolean;
  
  // Actions
  setQueues: (queues: { [key in ServiceType]?: QueueSummary }) => void;
  setCurrentQueue: (queue: QueueSummary | null) => void;
  setQueueStatistics: (stats: QueueStatistics | null) => void;
  setWaitTimeEstimations: (estimations: { [key in ServiceType]?: WaitTimeEstimation }) => void;
  setSelectedServiceType: (serviceType: ServiceType | null) => void;
  setSelectedFacilityId: (facilityId: string | null) => void;
  setLoading: (loading: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setAutoRefresh: (autoRefresh: boolean) => void;
  
  // Queue management actions
  addToQueue: (entry: QueueEntry) => void;
  updateQueueEntry: (queueId: string, updates: Partial<QueueEntry>) => void;
  removeFromQueue: (queueId: string) => void;
  updateQueueStatus: (queueId: string, status: QueueStatus) => void;
  
  // Utility actions
  clearQueues: () => void;
  refreshQueues: () => void;
  getQueueByService: (serviceType: ServiceType) => QueueSummary | null;
  getWaitTimeByService: (serviceType: ServiceType) => WaitTimeEstimation | null;
}

export const useQueueStore = create<QueueState>((set, get) => ({
  // Initial state
  queues: {},
  currentQueue: null,
  queueStatistics: null,
  waitTimeEstimations: {},
  selectedServiceType: null,
  selectedFacilityId: null,
  loading: false,
  updating: false,
  lastUpdated: null,
  autoRefresh: true,

  // Basic setters
  setQueues: (queues) => set({ queues, lastUpdated: new Date().toISOString() }),
  setCurrentQueue: (currentQueue) => set({ currentQueue }),
  setQueueStatistics: (queueStatistics) => set({ queueStatistics }),
  setWaitTimeEstimations: (waitTimeEstimations) => set({ waitTimeEstimations }),
  setSelectedServiceType: (selectedServiceType) => set({ selectedServiceType }),
  setSelectedFacilityId: (selectedFacilityId) => set({ selectedFacilityId }),
  setLoading: (loading) => set({ loading }),
  setUpdating: (updating) => set({ updating }),
  setAutoRefresh: (autoRefresh) => set({ autoRefresh }),

  // Queue management actions
  addToQueue: (entry) => {
    const { queues, selectedServiceType } = get();
    if (!selectedServiceType || !queues[selectedServiceType]) return;

    const updatedQueue = {
      ...queues[selectedServiceType]!,
      queue: [...queues[selectedServiceType]!.queue, entry],
      totalInQueue: queues[selectedServiceType]!.totalInQueue + 1,
      lastUpdated: new Date().toISOString()
    };

    set({
      queues: {
        ...queues,
        [selectedServiceType]: updatedQueue
      },
      currentQueue: selectedServiceType === get().currentQueue?.serviceType ? updatedQueue : get().currentQueue,
      lastUpdated: new Date().toISOString()
    });
  },

  updateQueueEntry: (queueId, updates) => {
    const { queues } = get();
    const updatedQueues = { ...queues };

    Object.keys(updatedQueues).forEach(serviceType => {
      const queue = updatedQueues[serviceType as ServiceType];
      if (queue) {
        const entryIndex = queue.queue.findIndex(entry => entry.queueId === queueId);
        if (entryIndex !== -1) {
          const updatedQueue = {
            ...queue,
            queue: queue.queue.map((entry, index) =>
              index === entryIndex ? { ...entry, ...updates } : entry
            ),
            lastUpdated: new Date().toISOString()
          };
          updatedQueues[serviceType as ServiceType] = updatedQueue;
        }
      }
    });

    set({
      queues: updatedQueues,
      lastUpdated: new Date().toISOString()
    });
  },

  removeFromQueue: (queueId) => {
    const { queues } = get();
    const updatedQueues = { ...queues };

    Object.keys(updatedQueues).forEach(serviceType => {
      const queue = updatedQueues[serviceType as ServiceType];
      if (queue) {
        const filteredQueue = queue.queue.filter(entry => entry.queueId !== queueId);
        if (filteredQueue.length !== queue.queue.length) {
          updatedQueues[serviceType as ServiceType] = {
            ...queue,
            queue: filteredQueue,
            totalInQueue: filteredQueue.length,
            lastUpdated: new Date().toISOString()
          };
        }
      }
    });

    set({
      queues: updatedQueues,
      lastUpdated: new Date().toISOString()
    });
  },

  updateQueueStatus: (queueId, status) => {
    const { updateQueueEntry } = get();
    const timestamp = new Date().toISOString();
    
    const updates: Partial<QueueEntry> = {
      status,
      ...(status === QueueStatus.Called && { calledAt: timestamp }),
      ...(status === QueueStatus.InService && { serviceStartedAt: timestamp }),
      ...(status === QueueStatus.Completed && { serviceCompletedAt: timestamp })
    };

    updateQueueEntry(queueId, updates);
  },

  // Utility actions
  clearQueues: () => set({
    queues: {},
    currentQueue: null,
    queueStatistics: null,
    waitTimeEstimations: {},
    lastUpdated: null
  }),

  refreshQueues: () => {
    // This would typically trigger a refetch from the API
    set({ lastUpdated: new Date().toISOString() });
  },

  getQueueByService: (serviceType) => {
    const { queues } = get();
    return queues[serviceType] || null;
  },

  getWaitTimeByService: (serviceType) => {
    const { waitTimeEstimations } = get();
    return waitTimeEstimations[serviceType] || null;
  }
}));
