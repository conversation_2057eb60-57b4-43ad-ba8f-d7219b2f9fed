import { useState, useEffect, useRef } from "react";
import { <PERSON>, Menu } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import SirobiltImage from "../../assets/images/logos/sirobilt.png";
import { toast } from "react-toastify";
import { FaCheckCircle } from "react-icons/fa";
import { useRoles } from "../../hooks/useRoles";

type Props = {
  toggleSidebar: () => void;
  onRegisterClick: () => void;
};

export const Navbar = ({ toggleSidebar, onRegisterClick }: Props) => {
  const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [username, setUsername] = useState<string | null>(null);
  const navigate = useNavigate();
  const quickActionsRef = useRef<HTMLDivElement | null>(null);
  const profileDropdownRef = useRef<HTMLDivElement | null>(null);
  const { hasRole } = useRoles();

  useEffect(() => {
    const storedUsername = localStorage.getItem("loggedInUser");
    setUsername(storedUsername);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (
        quickActionsRef.current &&
        !quickActionsRef.current.contains(target)
      ) {
        setIsQuickActionsOpen(false);
      }
      if (
        profileDropdownRef.current &&
        !profileDropdownRef.current.contains(target)
      ) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("loggedInUser");
    navigate("/login");

    setTimeout(() => {
      toast.info(
        <div className="flex items-start gap-2">
          <FaCheckCircle className="text-blue-600 mt-1" />
          <div>
            <strong>Logout successful</strong>
            <br />
            You have been logged out.
          </div>
        </div>,
        {
          style: {
            backgroundColor: "#eff6ff",
            border: "1px solid #3b82f6",
            color: "#1f2937",
            borderRadius: "8px",
            padding: "12px",
          },
          icon: false,
          position: "top-right",
          autoClose: 3000,
        }
      );
    }, 100);
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 bg-white shadow-sm w-full h-16">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <button className="block md:hidden" onClick={toggleSidebar}>
          <Menu className="w-6 h-6 text-gray-700" />
        </button>

        <Link to="/" className="flex items-center space-x-2">
          <img src={SirobiltImage} alt="Logo" className="h-30 w-auto" />
        </Link>

      </div>

      {/* Right Section */}
      <div className="flex items-center gap-4">
        {/* Quick Actions */}
        {!hasRole("doctor")}<div className="relative" ref={quickActionsRef}>
       {!hasRole("doctor")&&  <button
            onClick={() => setIsQuickActionsOpen((prev) => !prev)}
            className="flex items-center gap-2 px-4 py-1.5 border border-blue-600 text-blue-600 rounded-full cursor-pointer text-base font-medium bg-white hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-purple-400"
          >
            Quick Actions
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>}

          {isQuickActionsOpen && (
            <ul className="absolute right-0 mt-2 w-56 py-2 bg-white text-black rounded-lg shadow-lg z-50 border border-gray-200">
              {(hasRole("admin") || hasRole("receptionist")) && (
                <li>
                  <button
                    type="button"
                    onClick={() => {
                      onRegisterClick();
                      setIsQuickActionsOpen(false);
                    }}
                    className="w-full text-left px-4 py-2 hover:bg-gray-100"
                  >
                    Register Patient
                  </button>
                </li>
              )}
              <li>
                <Link to="/appointments" onClick={() => setIsQuickActionsOpen(false)} className="block px-4 py-2 hover:bg-gray-100">
                  Book Appointment
                </Link>
              </li>
              <li>
                <Link to="/queue" onClick={() => setIsQuickActionsOpen(false)} className="block px-4 py-2 hover:bg-gray-100">
                  Queue Management
                </Link>
              </li>
            </ul>
          )}
        </div>

        {/* Notifications */}
        <Bell className="w-5 h-5 text-gray-700 cursor-pointer" />

        {/* Avatar */}
        <div className="relative" ref={profileDropdownRef}>
          <div
            onClick={() => setIsProfileOpen((prev) => !prev)}
            className="avatar w-10 h-10 rounded-full ring ring-blue-400 ring-offset-2 cursor-pointer"
          >
            <div className="relative w-full h-full">
              <img
                src="https://randomuser.me/api/portraits/women/44.jpg"
                alt="User Profile"
                className="rounded-full object-cover w-full h-full"
              />
              <div className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
          </div>

          {isProfileOpen && (
            <ul className="absolute right-0 mt-2 w-44 py-2 bg-black text-white rounded-lg shadow-lg z-50">
              {username && (
                <li className="px-4 py-2 text-sm text-gray-300 cursor-default">
                  Logged in as{" "}
                  <span className="font-semibold text-purple-600">
                    {username + " " + localStorage.getItem("Facility")}
                  </span>
                </li>
              )}
              <li className="hover:bg-gray-700">
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2"
                >
                  Logout
                </button>
              </li>
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};
