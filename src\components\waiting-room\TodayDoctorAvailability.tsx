import React from 'react';
import { Users, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import type { DepartmentAvailableDoctors } from '../../types/department';

interface TodayDoctorAvailabilityProps {
  departments: DepartmentAvailableDoctors[];
  displayMode?: 'full' | 'compact' | 'summary';
  className?: string;
}

export const TodayDoctorAvailability: React.FC<TodayDoctorAvailabilityProps> = ({
  departments,
  displayMode = 'full',
  className = ''
}) => {
  const totalDoctors = departments.reduce((sum, dept) => sum + dept.totalDoctors, 0);
  const availableDoctors = departments.reduce((sum, dept) => sum + dept.availableDoctors, 0);
  const availabilityPercentage = totalDoctors > 0 ? Math.round((availableDoctors / totalDoctors) * 100) : 0;

  if (displayMode === 'summary') {
    return (
      <div className={`bg-white/10 backdrop-blur-sm rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Users className="text-blue-300" size={24} />
            <div>
              <div className="text-lg font-bold text-white">
                {availableDoctors}/{totalDoctors} Doctors Available
              </div>
              <div className="text-sm text-blue-200">
                {availabilityPercentage}% availability today
              </div>
            </div>
          </div>
          <div className={`text-3xl font-bold ${
            availabilityPercentage >= 80 ? 'text-green-300' :
            availabilityPercentage >= 60 ? 'text-yellow-300' : 'text-red-300'
          }`}>
            {availabilityPercentage}%
          </div>
        </div>
      </div>
    );
  }

  if (displayMode === 'compact') {
    return (
      <div className={`bg-white/10 backdrop-blur-sm rounded-lg p-4 ${className}`}>
        <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
          <Users className="mr-2" size={20} />
          Today's Doctor Availability
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {departments.map((dept) => (
            <div key={dept.departmentId} className="text-center">
              <div className="bg-white/20 rounded-lg p-3">
                <div className="text-xl font-bold text-yellow-300">
                  {dept.availableDoctors}/{dept.totalDoctors}
                </div>
                <div className="text-xs text-blue-200">{dept.departmentCode}</div>
                <div className="text-xs text-white/80 truncate">{dept.departmentName}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Full display mode
  return (
    <div className={`bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 ${className}`}>
      <div className="p-6 border-b border-white/20">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Users className="mr-3" size={28} />
            Today's Doctor Availability - OPD
          </h2>
          <div className="text-right">
            <div className="text-3xl font-bold text-yellow-300">
              {availableDoctors}/{totalDoctors}
            </div>
            <div className="text-sm text-blue-200">Doctors Available</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Department Summary Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          {departments.map((dept) => (
            <div key={dept.departmentId} className="text-center">
              <div className="bg-white/20 rounded-lg p-4">
                <div className="text-2xl font-bold text-yellow-300">
                  {dept.availableDoctors}/{dept.totalDoctors}
                </div>
                <div className="text-sm text-blue-200 font-medium">{dept.departmentCode}</div>
                <div className="text-xs text-white/80">{dept.departmentName}</div>
                <div className={`text-xs mt-1 ${
                  dept.availableDoctors === dept.totalDoctors ? 'text-green-300' :
                  dept.availableDoctors > 0 ? 'text-yellow-300' : 'text-red-300'
                }`}>
                  {dept.availableDoctors === dept.totalDoctors ? 'All Available' :
                   dept.availableDoctors > 0 ? 'Partially Available' : 'None Available'}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Detailed Doctor List */}
        <div className="space-y-4">
          {departments.map((dept) => (
            <div key={dept.departmentId} className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white">
                  {dept.departmentName} ({dept.departmentCode})
                </h3>
                <div className="text-sm text-blue-200">
                  {dept.availableDoctors} of {dept.totalDoctors} available
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {dept.doctors.map((doctor) => (
                  <div
                    key={doctor.providerId}
                    className={`rounded-lg p-3 border ${
                      doctor.isAvailable 
                        ? 'bg-green-500/10 border-green-500/30' 
                        : doctor.hasScheduleToday
                        ? 'bg-yellow-500/10 border-yellow-500/30'
                        : 'bg-red-500/10 border-red-500/30'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <div className="font-medium text-sm text-white">
                            {doctor.name}
                          </div>
                          {doctor.isAvailable ? (
                            <CheckCircle className="text-green-400" size={14} />
                          ) : doctor.hasScheduleToday ? (
                            <AlertCircle className="text-yellow-400" size={14} />
                          ) : (
                            <XCircle className="text-red-400" size={14} />
                          )}
                        </div>
                        <div className="text-xs text-gray-300 mb-1">
                          {doctor.specialization}
                        </div>
                        <div className={`text-xs font-medium ${
                          doctor.currentStatus === 'Available' ? 'text-green-300' :
                          doctor.currentStatus === 'Busy' ? 'text-yellow-300' :
                          doctor.currentStatus === 'Offline' ? 'text-red-300' : 'text-gray-300'
                        }`}>
                          {doctor.currentStatus}
                        </div>
                        {doctor.scheduleTime && (
                          <div className="text-xs text-blue-300 flex items-center mt-1">
                            <Clock size={10} className="mr-1" />
                            {doctor.scheduleTime}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        {doctor.isAvailable && (
                          <>
                            <div className="text-lg font-bold text-blue-300">
                              {doctor.currentQueueLength}
                            </div>
                            <div className="text-xs text-gray-400">in queue</div>
                          </>
                        )}
                        {doctor.nextAvailableTime && (
                          <div className="text-xs text-yellow-300">
                            Next: {doctor.nextAvailableTime}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Overall Statistics */}
        <div className="mt-6 bg-white/5 rounded-lg p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-300">{availableDoctors}</div>
              <div className="text-xs text-gray-400">Available Now</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-300">
                {departments.reduce((sum, dept) => sum + dept.doctors.filter(d => d.hasScheduleToday && !d.isAvailable).length, 0)}
              </div>
              <div className="text-xs text-gray-400">Scheduled Later</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-300">
                {departments.reduce((sum, dept) => sum + dept.doctors.filter(d => !d.hasScheduleToday).length, 0)}
              </div>
              <div className="text-xs text-gray-400">No Schedule</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-300">
                {departments.reduce((sum, dept) => sum + dept.doctors.reduce((docSum, doc) => docSum + doc.patientsToday, 0), 0)}
              </div>
              <div className="text-xs text-gray-400">Patients Seen Today</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
