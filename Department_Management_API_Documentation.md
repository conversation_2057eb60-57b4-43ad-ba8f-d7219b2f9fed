# 🏥 Department Management & Provider-Department Mapping API Documentation

**Version:** 1.0
**Date:** December 2024
**Project:** MeghaSanjeevini Hospital Management System

---

## 📋 Table of Contents

1. [Overview](#overview)
2. [Database Schema](#database-schema)
3. [API Endpoints](#api-endpoints)
4. [Data Models](#data-models)
5. [Authentication & Authorization](#authentication--authorization)
6. [Error Handling](#error-handling)
7. [Implementation Guidelines](#implementation-guidelines)

---

## 🎯 Overview

This document provides comprehensive API specifications and database schema for the Department Management and Provider-Department Mapping system. The system enables:

- **Department Management**: Create, read, update, delete hospital departments
- **Provider-Department Mapping**: Assign providers to departments with roles
- **Queue Management**: Real-time department-wise queue tracking
- **Doctor Availability**: Track today's available doctors by department

### Key Features
- ✅ Complete CRUD operations for departments
- ✅ Provider-department relationship management
- ✅ Real-time queue tracking by department
- ✅ Doctor availability monitoring
- ✅ Role-based access control
- ✅ Audit trail for all operations

---

## 🗄️ Database Schema

### **1. departments Table**

```sql
CREATE TABLE departments (
    department_id VARCHAR(50) PRIMARY KEY,
    facility_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) NOT NULL,
    description TEXT,

    -- Department Head
    head_of_department VARCHAR(50), -- References providers.provider_id

    -- Contact Information
    phone_number VARCHAR(20),
    email VARCHAR(100),
    location VARCHAR(200),

    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_emergency_department BOOLEAN DEFAULT FALSE,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),

    -- Indexes
    INDEX idx_facility_id (facility_id),
    INDEX idx_department_code (code),
    INDEX idx_department_name (name),
    INDEX idx_is_active (is_active),

    -- Constraints
    UNIQUE KEY unique_facility_code (facility_id, code),
    FOREIGN KEY (facility_id) REFERENCES facilities(facility_id),
    FOREIGN KEY (head_of_department) REFERENCES providers(provider_id),
    FOREIGN KEY (created_by) REFERENCES users(user_id),
    FOREIGN KEY (updated_by) REFERENCES users(user_id)
);
```

### **2. department_operating_hours Table**

```sql
CREATE TABLE department_operating_hours (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    department_id VARCHAR(50) NOT NULL,
    day_of_week ENUM('MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY') NOT NULL,
    is_operating BOOLEAN DEFAULT TRUE,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_start_time TIME,
    break_end_time TIME,
    emergency_hours BOOLEAN DEFAULT FALSE,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_department_id (department_id),
    INDEX idx_day_of_week (day_of_week),

    -- Constraints
    UNIQUE KEY unique_dept_day (department_id, day_of_week),
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE CASCADE
);
```

### **3. department_services Table**

```sql
CREATE TABLE department_services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    department_id VARCHAR(50) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    service_code VARCHAR(20),
    description TEXT,

    -- Service Details
    average_service_time INT DEFAULT 15, -- in minutes
    max_concurrent_patients INT DEFAULT 1,
    requires_appointment BOOLEAN DEFAULT TRUE,
    allows_walk_in BOOLEAN DEFAULT FALSE,

    -- Pricing
    base_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'INR',

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_department_id (department_id),
    INDEX idx_service_code (service_code),
    INDEX idx_is_active (is_active),

    -- Constraints
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE CASCADE
);
```

### **4. provider_department_mappings Table**

```sql
CREATE TABLE provider_department_mappings (
    mapping_id VARCHAR(50) PRIMARY KEY,
    provider_id VARCHAR(50) NOT NULL,
    department_id VARCHAR(50) NOT NULL,
    facility_id VARCHAR(50) NOT NULL,

    -- Role in Department
    role ENUM('HEAD', 'SENIOR', 'JUNIOR', 'CONSULTANT', 'RESIDENT', 'INTERN', 'VISITING') NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,

    -- Effective Period
    effective_from DATE NOT NULL,
    effective_to DATE,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    assigned_by VARCHAR(50),

    -- Indexes
    INDEX idx_provider_id (provider_id),
    INDEX idx_department_id (department_id),
    INDEX idx_facility_id (facility_id),
    INDEX idx_is_primary (is_primary),
    INDEX idx_is_active (is_active),
    INDEX idx_effective_dates (effective_from, effective_to),

    -- Constraints
    UNIQUE KEY unique_provider_dept_active (provider_id, department_id, is_active),
    FOREIGN KEY (provider_id) REFERENCES providers(provider_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (facility_id) REFERENCES facilities(facility_id),
    FOREIGN KEY (assigned_by) REFERENCES users(user_id)
);
```

### **5. department_stats Table** (Optional - for caching)

```sql
CREATE TABLE department_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    department_id VARCHAR(50) NOT NULL,
    stat_date DATE NOT NULL,

    -- Provider Statistics
    total_providers INT DEFAULT 0,
    active_providers INT DEFAULT 0,
    available_providers INT DEFAULT 0,

    -- Appointment Statistics
    appointments_today INT DEFAULT 0,
    appointments_this_week INT DEFAULT 0,
    appointments_this_month INT DEFAULT 0,

    -- Queue Statistics
    current_queue_length INT DEFAULT 0,
    average_wait_time INT DEFAULT 0, -- in minutes

    -- Utilization
    utilization_rate DECIMAL(5,2) DEFAULT 0.00, -- percentage
    peak_hours JSON, -- ["10:00-11:00", "14:00-15:00"]

    -- Metadata
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_department_id (department_id),
    INDEX idx_stat_date (stat_date),
    INDEX idx_last_updated (last_updated),

    -- Constraints
    UNIQUE KEY unique_dept_date (department_id, stat_date),
    FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE CASCADE
);
```

---

## 🔗 API Endpoints

### **Base URL**: `https://api.meghasanjeevini.com/api`

### **Authentication**: All endpoints require Bearer token authentication

---

## 📋 Department Management APIs

### **1. Get All Departments**

```http
GET /api/departments
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| facilityId | string | No | Filter by facility ID |
| isActive | boolean | No | Filter by active status |
| isEmergencyDepartment | boolean | No | Filter emergency departments |
| hasProviders | boolean | No | Filter departments with providers |
| searchTerm | string | No | Search in name, code, description |
| page | number | No | Page number (default: 0) |
| size | number | No | Page size (default: 20) |

**Example Request:**
```bash
curl -X GET "https://api.meghasanjeevini.com/api/departments?facilityId=fac-001&isActive=true&page=0&size=10" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "departmentId": "dept-001",
        "facilityId": "fac-001",
        "name": "Cardiology",
        "code": "CARD",
        "description": "Heart and cardiovascular diseases",
        "headOfDepartment": "prov-001",
        "phoneNumber": "+91-11-2345-6789",
        "email": "<EMAIL>",
        "location": "2nd Floor, Block A",
        "operatingHours": [
          {
            "dayOfWeek": "MONDAY",
            "isOperating": true,
            "startTime": "08:00:00",
            "endTime": "18:00:00",
            "breakStartTime": "13:00:00",
            "breakEndTime": "14:00:00"
          }
        ],
        "services": ["Cardiac Consultation", "ECG", "Echocardiography"],
        "isActive": true,
        "isEmergencyDepartment": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "totalElements": 5,
    "totalPages": 1,
    "currentPage": 0
  }
}
```

### **2. Get Department by ID**

```http
GET /api/departments/{departmentId}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| departmentId | string | Yes | Department ID |

**Example Request:**
```bash
curl -X GET "https://api.meghasanjeevini.com/api/departments/dept-001" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "name": "Cardiology",
    "code": "CARD",
    "description": "Heart and cardiovascular diseases",
    "headOfDepartment": "prov-001",
    "phoneNumber": "+91-11-2345-6789",
    "email": "<EMAIL>",
    "location": "2nd Floor, Block A",
    "operatingHours": [...],
    "services": ["Cardiac Consultation", "ECG", "Echocardiography"],
    "isActive": true,
    "isEmergencyDepartment": false,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### **3. Create Department**

```http
POST /api/departments
```

**Request Body:**
```json
{
  "facilityId": "fac-001",
  "name": "Cardiology",
  "code": "CARD",
  "description": "Heart and cardiovascular diseases",
  "phoneNumber": "+91-11-2345-6789",
  "email": "<EMAIL>",
  "location": "2nd Floor, Block A",
  "operatingHours": [
    {
      "dayOfWeek": "MONDAY",
      "isOperating": true,
      "startTime": "08:00:00",
      "endTime": "18:00:00",
      "breakStartTime": "13:00:00",
      "breakEndTime": "14:00:00"
    }
  ],
  "services": ["Cardiac Consultation", "ECG", "Echocardiography"],
  "isActive": true,
  "isEmergencyDepartment": false,
  "headOfDepartment": "prov-001"
}
```

**Example Request:**
```bash
curl -X POST "https://api.meghasanjeevini.com/api/departments" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "facilityId": "fac-001",
    "name": "Cardiology",
    "code": "CARD",
    "description": "Heart and cardiovascular diseases",
    "isActive": true,
    "isEmergencyDepartment": false
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "name": "Cardiology",
    "code": "CARD",
    "description": "Heart and cardiovascular diseases",
    "isActive": true,
    "isEmergencyDepartment": false,
    "createdAt": "2024-12-19T10:30:00Z",
    "updatedAt": "2024-12-19T10:30:00Z"
  }
}
```

### **4. Update Department**

```http
PUT /api/departments/{departmentId}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| departmentId | string | Yes | Department ID |

**Request Body:**
```json
{
  "name": "Updated Cardiology",
  "description": "Updated description",
  "phoneNumber": "+91-11-2345-6790",
  "email": "<EMAIL>",
  "location": "3rd Floor, Block A",
  "isActive": true,
  "headOfDepartment": "prov-002"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "name": "Updated Cardiology",
    "code": "CARD",
    "description": "Updated description",
    "updatedAt": "2024-12-19T11:30:00Z"
  }
}
```

### **5. Delete Department**

```http
DELETE /api/departments/{departmentId}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| departmentId | string | Yes | Department ID |

**Example Request:**
```bash
curl -X DELETE "https://api.meghasanjeevini.com/api/departments/dept-001" \
  -H "Authorization: Bearer {token}"
```

**Response:**
```json
{
  "success": true,
  "message": "Department deleted successfully"
}
```

### **6. Get Department Statistics**

```http
GET /api/departments/{departmentId}/stats
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| departmentId | string | Yes | Department ID |

**Response:**
```json
{
  "success": true,
  "data": {
    "departmentId": "dept-001",
    "departmentName": "Cardiology",
    "totalProviders": 5,
    "activeProviders": 4,
    "availableProviders": 3,
    "appointmentsToday": 25,
    "appointmentsThisWeek": 150,
    "appointmentsThisMonth": 600,
    "currentQueueLength": 8,
    "averageWaitTime": 15,
    "utilizationRate": 85.5,
    "peakHours": ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
    "lastUpdated": "2024-12-19T12:00:00Z"
  }
}
```

---

## 👥 Provider-Department Mapping APIs

### **7. Get Provider-Department Mappings**

```http
GET /api/provider-department-mappings
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| providerId | string | No | Filter by provider ID |
| departmentId | string | No | Filter by department ID |
| facilityId | string | No | Filter by facility ID |
| isActive | boolean | No | Filter by active status |
| isPrimary | boolean | No | Filter primary mappings |

**Example Request:**
```bash
curl -X GET "https://api.meghasanjeevini.com/api/provider-department-mappings?facilityId=fac-001&isActive=true" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "mappingId": "map-001",
      "providerId": "prov-001",
      "departmentId": "dept-001",
      "facilityId": "fac-001",
      "role": "HEAD",
      "isPrimary": true,
      "effectiveFrom": "2024-01-01",
      "effectiveTo": null,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "assignedBy": "user-001"
    }
  ]
}
```

### **8. Create Provider-Department Mapping**

```http
POST /api/provider-department-mappings
```

**Request Body:**
```json
{
  "providerId": "prov-001",
  "departmentId": "dept-001",
  "facilityId": "fac-001",
  "role": "CONSULTANT",
  "isPrimary": false,
  "effectiveFrom": "2024-12-19",
  "effectiveTo": null,
  "isActive": true
}
```

**Example Request:**
```bash
curl -X POST "https://api.meghasanjeevini.com/api/provider-department-mappings" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "providerId": "prov-001",
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "role": "CONSULTANT",
    "isPrimary": false,
    "effectiveFrom": "2024-12-19",
    "isActive": true
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "mappingId": "map-002",
    "providerId": "prov-001",
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "role": "CONSULTANT",
    "isPrimary": false,
    "effectiveFrom": "2024-12-19",
    "isActive": true,
    "createdAt": "2024-12-19T10:30:00Z"
  }
}
```

### **9. Update Provider-Department Mapping**

```http
PUT /api/provider-department-mappings/{mappingId}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| mappingId | string | Yes | Mapping ID |

**Request Body:**
```json
{
  "role": "SENIOR",
  "isPrimary": true,
  "effectiveFrom": "2024-12-19",
  "effectiveTo": "2025-12-19",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "mappingId": "map-002",
    "providerId": "prov-001",
    "departmentId": "dept-001",
    "facilityId": "fac-001",
    "role": "SENIOR",
    "isPrimary": true,
    "effectiveFrom": "2024-12-19",
    "effectiveTo": "2025-12-19",
    "isActive": true,
    "updatedAt": "2024-12-19T11:30:00Z"
  }
}
```

### **10. Delete Provider-Department Mapping**

```http
DELETE /api/provider-department-mappings/{mappingId}
```

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| mappingId | string | Yes | Mapping ID |

**Example Request:**
```bash
curl -X DELETE "https://api.meghasanjeevini.com/api/provider-department-mappings/map-002" \
  -H "Authorization: Bearer {token}"
```

**Response:**
```json
{
  "success": true,
  "message": "Provider-Department mapping deleted successfully"
}
```

---

## 🏥 Queue Management APIs

### **11. Get Department Queues**

```http
GET /api/departments/queues
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| facilityId | string | Yes | Facility ID |
| date | string | No | Date in ISO format (YYYY-MM-DD) |

**Example Request:**
```bash
curl -X GET "https://api.meghasanjeevini.com/api/departments/queues?facilityId=fac-001&date=2024-12-19" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "departmentId": "dept-001",
      "departmentName": "Cardiology",
      "departmentCode": "CARD",
      "totalInQueue": 8,
      "currentlyServing": {
        "providerId": "prov-001",
        "providerName": "Dr. Sarah Johnson",
        "patientName": "John Doe",
        "queueNumber": 1,
        "serviceStartTime": "2024-12-19T10:30:00Z"
      },
      "providerQueues": [
        {
          "providerId": "prov-001",
          "providerName": "Dr. Sarah Johnson",
          "providerTitle": "Dr.",
          "specialization": "Cardiology",
          "queueLength": 5,
          "currentPatient": {
            "patientName": "John Doe",
            "queueNumber": 1,
            "serviceStartTime": "2024-12-19T10:30:00Z"
          },
          "nextPatients": [
            {
              "patientName": "Jane Smith",
              "queueNumber": 2,
              "estimatedTime": "2024-12-19T10:45:00Z"
            }
          ],
          "isAvailable": true,
          "status": "BUSY",
          "averageServiceTime": 15,
          "estimatedWaitTime": 45
        }
      ],
      "averageWaitTime": 20,
      "estimatedWaitTime": 35,
      "peakHours": ["10:00-11:00", "14:00-15:00"],
      "lastUpdated": "2024-12-19T10:30:00Z"
    }
  ]
}
```

### **12. Get Today's Available Doctors**

```http
GET /api/departments/available-doctors
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| facilityId | string | Yes | Facility ID |
| date | string | No | Date in ISO format (YYYY-MM-DD) |

**Example Request:**
```bash
curl -X GET "https://api.meghasanjeevini.com/api/departments/available-doctors?facilityId=fac-001&date=2024-12-19" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "departmentId": "dept-001",
      "departmentName": "Cardiology",
      "departmentCode": "CARD",
      "totalDoctors": 5,
      "availableDoctors": 3,
      "doctors": [
        {
          "providerId": "prov-001",
          "name": "Dr. Sarah Johnson",
          "title": "Dr.",
          "specialization": "Cardiology",
          "isAvailable": true,
          "hasScheduleToday": true,
          "scheduleTime": "09:00 - 17:00",
          "currentStatus": "Available",
          "nextAvailableTime": null,
          "patientsToday": 12,
          "currentQueueLength": 5
        }
      ]
    }
  ]
}
```

---

## 📊 Data Models

### **Department Model**
```typescript
interface Department {
  departmentId: string;
  facilityId: string;
  name: string;
  code: string;
  description?: string;

  // Department Head
  headOfDepartment?: string; // Provider ID

  // Contact Information
  phoneNumber?: string;
  email?: string;
  location?: string;

  // Operational Details
  operatingHours: DepartmentOperatingHours[];
  services: string[];

  // Status
  isActive: boolean;
  isEmergencyDepartment: boolean;

  // Metadata
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}
```

### **DepartmentOperatingHours Model**
```typescript
interface DepartmentOperatingHours {
  dayOfWeek: string; // "MONDAY", "TUESDAY", etc.
  isOperating: boolean;
  startTime: string; // "08:00:00"
  endTime: string; // "18:00:00"
  breakStartTime?: string;
  breakEndTime?: string;
  emergencyHours?: boolean; // 24/7 for emergency departments
}
```

### **ProviderDepartmentMapping Model**
```typescript
interface ProviderDepartmentMapping {
  mappingId: string;
  providerId: string;
  departmentId: string;
  facilityId: string;

  // Role in Department
  role: DepartmentRole;
  isPrimary: boolean;

  // Effective Period
  effectiveFrom: string;
  effectiveTo?: string;

  // Status
  isActive: boolean;

  // Metadata
  createdAt?: string;
  updatedAt?: string;
  assignedBy?: string;
}
```

### **DepartmentRole Enum**
```typescript
enum DepartmentRole {
  Head = "HEAD",
  Senior = "SENIOR",
  Junior = "JUNIOR",
  Consultant = "CONSULTANT",
  Resident = "RESIDENT",
  Intern = "INTERN",
  Visiting = "VISITING"
}
```

### **DepartmentStats Model**
```typescript
interface DepartmentStats {
  departmentId: string;
  departmentName: string;

  // Provider Statistics
  totalProviders: number;
  activeProviders: number;
  availableProviders: number;

  // Appointment Statistics
  appointmentsToday: number;
  appointmentsThisWeek: number;
  appointmentsThisMonth: number;

  // Queue Statistics
  currentQueueLength: number;
  averageWaitTime: number; // in minutes

  // Utilization
  utilizationRate: number; // percentage
  peakHours: string[];

  // Last Updated
  lastUpdated: string;
}
```

### **DepartmentQueue Model**
```typescript
interface DepartmentQueue {
  departmentId: string;
  departmentName: string;
  departmentCode: string;

  // Queue Information
  totalInQueue: number;
  currentlyServing?: {
    providerId: string;
    providerName: string;
    patientName: string;
    queueNumber: number;
    serviceStartTime: string;
  };

  // Provider Queues
  providerQueues: ProviderQueueInfo[];

  // Department Statistics
  averageWaitTime: number;
  estimatedWaitTime: number;
  peakHours: string[];

  // Last Updated
  lastUpdated: string;
}
```

### **ProviderQueueInfo Model**
```typescript
interface ProviderQueueInfo {
  providerId: string;
  providerName: string;
  providerTitle: string;
  specialization: string;

  // Queue Details
  queueLength: number;
  currentPatient?: {
    patientName: string;
    queueNumber: number;
    serviceStartTime: string;
  };
  nextPatients: {
    patientName: string;
    queueNumber: number;
    estimatedTime: string;
  }[];

  // Provider Status
  isAvailable: boolean;
  status: string; // "AVAILABLE", "BUSY", "BREAK", "OFFLINE"

  // Wait Time
  averageServiceTime: number;
  estimatedWaitTime: number;
}
```

### **TodayAvailableDoctor Model**
```typescript
interface TodayAvailableDoctor {
  providerId: string;
  name: string;
  title: string;
  specialization: string;
  isAvailable: boolean;
  hasScheduleToday: boolean;
  scheduleTime: string | null;
  currentStatus: string; // "Available", "Busy", "Offline", "No Schedule"
  nextAvailableTime: string | null;
  patientsToday: number;
  currentQueueLength: number;
}
```

### **DepartmentAvailableDoctors Model**
```typescript
interface DepartmentAvailableDoctors {
  departmentId: string;
  departmentName: string;
  departmentCode: string;
  totalDoctors: number;
  availableDoctors: number;
  doctors: TodayAvailableDoctor[];
}
```

---

## 🔐 Authentication & Authorization

### **Authentication**
All API endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer {jwt_token}
```

### **Required Headers**
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Facility-ID: {facility_id} (optional, for multi-facility support)
```

### **Role-Based Access Control**

| Role | Permissions |
|------|-------------|
| **Super Admin** | Full access to all operations |
| **Hospital Admin** | Full access within facility |
| **Department Head** | Full access to own department |
| **Doctor** | Read access to own department, limited updates |
| **Nurse** | Read access to assigned departments |
| **Receptionist** | Read access for queue management |

### **Permission Matrix**

| Operation | Super Admin | Hospital Admin | Dept Head | Doctor | Nurse | Receptionist |
|-----------|-------------|----------------|-----------|--------|-------|--------------|
| Create Department | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Update Department | ✅ | ✅ | ✅ (own) | ❌ | ❌ | ❌ |
| Delete Department | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| View Departments | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Create Mapping | ✅ | ✅ | ✅ (own dept) | ❌ | ❌ | ❌ |
| Update Mapping | ✅ | ✅ | ✅ (own dept) | ❌ | ❌ | ❌ |
| Delete Mapping | ✅ | ✅ | ✅ (own dept) | ❌ | ❌ | ❌ |
| View Mappings | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| View Queues | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| View Doctor Availability | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

---

## ⚠️ Error Handling

### **Standard Error Response Format**
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "path": "/api/departments"
}
```

### **HTTP Status Codes**

| Status Code | Description | Example |
|-------------|-------------|---------|
| **200** | Success | Request completed successfully |
| **201** | Created | Resource created successfully |
| **400** | Bad Request | Invalid request parameters |
| **401** | Unauthorized | Invalid or missing authentication |
| **403** | Forbidden | Insufficient permissions |
| **404** | Not Found | Resource not found |
| **409** | Conflict | Resource already exists |
| **422** | Unprocessable Entity | Validation errors |
| **500** | Internal Server Error | Server error |

### **Common Error Codes**

| Error Code | Description |
|------------|-------------|
| `DEPT_NOT_FOUND` | Department not found |
| `DEPT_CODE_EXISTS` | Department code already exists |
| `MAPPING_NOT_FOUND` | Provider-department mapping not found |
| `MAPPING_EXISTS` | Mapping already exists |
| `INVALID_ROLE` | Invalid department role |
| `INVALID_DATE_RANGE` | Invalid effective date range |
| `PROVIDER_NOT_FOUND` | Provider not found |
| `FACILITY_NOT_FOUND` | Facility not found |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |

### **Validation Error Examples**

**400 Bad Request - Validation Errors:**
```json
{
  "success": false,
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "details": {
    "name": "Department name is required",
    "code": "Department code must be 2-10 characters",
    "email": "Invalid email format"
  }
}
```

**409 Conflict - Duplicate Resource:**
```json
{
  "success": false,
  "error": "Department code already exists in this facility",
  "code": "DEPT_CODE_EXISTS",
  "details": {
    "facilityId": "fac-001",
    "code": "CARD"
  }
}
```

---

## 🛠️ Implementation Guidelines

### **Database Considerations**

1. **Indexing Strategy**
   - Primary keys on all ID fields
   - Composite indexes on frequently queried combinations
   - Index on `facility_id` for multi-tenant support
   - Index on `is_active` for filtering active records

2. **Data Integrity**
   - Foreign key constraints for referential integrity
   - Unique constraints on business keys (facility_id + code)
   - Check constraints for valid enum values
   - Cascade deletes where appropriate

3. **Performance Optimization**
   - Use connection pooling
   - Implement query result caching for static data
   - Consider read replicas for reporting queries
   - Optimize queries with proper JOIN strategies

### **API Implementation Best Practices**

1. **Input Validation**
   - Validate all input parameters
   - Sanitize string inputs to prevent injection
   - Implement rate limiting
   - Use request size limits

2. **Response Formatting**
   - Consistent response structure
   - Include pagination metadata
   - Provide meaningful error messages
   - Use appropriate HTTP status codes

3. **Security**
   - Implement JWT token validation
   - Role-based access control
   - Audit logging for all operations
   - Input sanitization and validation

4. **Monitoring & Logging**
   - Log all API requests and responses
   - Monitor API performance metrics
   - Implement health check endpoints
   - Track error rates and response times

### **Caching Strategy**

1. **Redis Caching**
   ```
   Key Pattern: dept:{facilityId}:list
   TTL: 300 seconds (5 minutes)

   Key Pattern: dept:{departmentId}:details
   TTL: 600 seconds (10 minutes)

   Key Pattern: mapping:{facilityId}:list
   TTL: 300 seconds (5 minutes)
   ```

2. **Cache Invalidation**
   - Invalidate on department create/update/delete
   - Invalidate mappings on provider assignment changes
   - Use cache tags for efficient bulk invalidation

### **Real-time Updates**

1. **WebSocket Events**
   ```json
   {
     "event": "department_queue_updated",
     "data": {
       "departmentId": "dept-001",
       "queueLength": 8,
       "currentlyServing": {...}
     }
   }
   ```

2. **Event Types**
   - `department_created`
   - `department_updated`
   - `department_deleted`
   - `provider_assigned`
   - `provider_unassigned`
   - `queue_updated`
   - `doctor_availability_changed`

### **Testing Requirements**

1. **Unit Tests**
   - Test all API endpoints
   - Test validation logic
   - Test error handling
   - Test permission checks

2. **Integration Tests**
   - Test database operations
   - Test external service integrations
   - Test authentication flows
   - Test real-time updates

3. **Load Testing**
   - Test concurrent user scenarios
   - Test high-volume queue updates
   - Test database performance under load
   - Test cache performance

### **Deployment Considerations**

1. **Environment Configuration**
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=meghasanjeevini
   DB_USER=api_user
   DB_PASSWORD=secure_password

   REDIS_HOST=localhost
   REDIS_PORT=6379

   JWT_SECRET=your_jwt_secret
   JWT_EXPIRY=24h

   API_RATE_LIMIT=1000
   API_RATE_WINDOW=3600
   ```

2. **Health Checks**
   ```http
   GET /api/health
   ```

   Response:
   ```json
   {
     "status": "healthy",
     "database": "connected",
     "redis": "connected",
     "version": "1.0.0",
     "timestamp": "2024-12-19T10:30:00Z"
   }
   ```

3. **Monitoring Endpoints**
   ```http
   GET /api/metrics
   GET /api/health/detailed
   ```

---

## 📋 API Testing Examples

### **Postman Collection**

```json
{
  "info": {
    "name": "Department Management API",
    "description": "API collection for department management and provider mapping"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{jwt_token}}",
        "type": "string"
      }
    ]
  },
  "variable": [
    {
      "key": "base_url",
      "value": "https://api.meghasanjeevini.com/api"
    },
    {
      "key": "facility_id",
      "value": "fac-001"
    }
  ]
}
```

### **Sample Test Scripts**

```javascript
// Test: Create Department
pm.test("Create department successfully", function () {
    pm.response.to.have.status(201);
    pm.response.to.be.json;

    const response = pm.response.json();
    pm.expect(response.success).to.be.true;
    pm.expect(response.data.departmentId).to.exist;

    // Store department ID for subsequent tests
    pm.environment.set("department_id", response.data.departmentId);
});

// Test: Get Department
pm.test("Get department by ID", function () {
    pm.response.to.have.status(200);

    const response = pm.response.json();
    pm.expect(response.success).to.be.true;
    pm.expect(response.data.name).to.equal("Cardiology");
});
```

---

## 📞 Support & Contact

For technical support and questions regarding this API documentation:

- **Email**: <EMAIL>
- **Documentation**: https://docs.meghasanjeevini.com
- **Issue Tracker**: https://github.com/meghasanjeevini/api-issues

---

**Document Version**: 1.0
**Last Updated**: December 19, 2024
**Next Review**: January 19, 2025